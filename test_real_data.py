#!/usr/bin/env python3
"""
Test-Script für echte Marktdaten und ICT-Analyse
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from real_market_data import real_market_data
from real_ict_analyzer import real_ict_analyzer
import json
from datetime import datetime

def test_market_data():
    """Teste Marktdaten-Provider"""
    print("🔍 Teste Marktdaten-Provider...")
    
    # Teste aktuellen Preis
    current_price = real_market_data.get_current_price()
    print(f"✅ Aktueller Gold-Preis: ${current_price['price']:.2f}")
    print(f"   Quelle: {current_price['source']}")
    print(f"   Änderung: {current_price['change']:+.2f} ({current_price['change_percent']:+.2f}%)")
    
    # Teste historische Daten
    historical_data = real_market_data.get_historical_data(days=7)
    print(f"✅ Historische Daten: {len(historical_data)} Datenpunkte")
    
    if historical_data:
        latest = historical_data[-1]
        print(f"   Letzter Eintrag: {latest['timestamp']}")
        print(f"   OHLC: O:{latest['open']:.2f} H:{latest['high']:.2f} L:{latest['low']:.2f} C:{latest['close']:.2f}")
    
    # Teste Marktstatus
    market_status = real_market_data.get_market_status()
    print(f"✅ Marktstatus: {market_status['session']} (Aktivität: {market_status['activity_level']})")
    
    return historical_data

def test_ict_analysis(historical_data):
    """Teste ICT-Analyse mit echten Daten"""
    print("\n🧠 Teste ICT-Analyse...")
    
    if not historical_data:
        print("❌ Keine historischen Daten für Analyse verfügbar")
        return None
    
    # Führe ICT-Analyse durch
    analysis = real_ict_analyzer.analyze_market_data(historical_data)
    
    print(f"✅ Analyse abgeschlossen - {analysis['data_points_analyzed']} Datenpunkte analysiert")
    print(f"   Gesamt-Konfidenz: {analysis['confidence_score']:.1%}")
    
    # Marktstruktur
    ms = analysis['market_structure']
    print(f"   📈 Marktstruktur: {ms['trend']} (Stärke: {ms['strength']:.1%}, Konfidenz: {ms['confidence']:.1%})")
    
    # Fair Value Gaps
    fvgs = analysis['fair_value_gaps']
    print(f"   ⚡ Fair Value Gaps: {len(fvgs)} erkannt")
    for i, gap in enumerate(fvgs[:3]):  # Zeige top 3
        status = "gefüllt" if gap['filled'] else "ungefüllt"
        print(f"      {i+1}. {gap['type']} FVG: ${gap['start_price']:.2f}-${gap['end_price']:.2f} ({status})")
    
    # Liquiditätspools
    pools = analysis['liquidity_pools']
    print(f"   🌊 Liquiditätspools: {len(pools)} erkannt")
    for i, pool in enumerate(pools[:3]):  # Zeige top 3
        status = "abgeholt" if pool['swept'] else "verfügbar"
        print(f"      {i+1}. {pool['type']} bei ${pool['price']:.2f} (Stärke: {pool['strength']:.1%}, {status})")
    
    # Premium/Discount
    pd = analysis['premium_discount']
    print(f"   💰 Premium/Discount: {pd['zone']} (Risiko: {pd['risk_level']})")
    
    # Trading-Signal
    signal = analysis['trading_signal']
    if signal:
        print(f"   🎯 Trading-Signal: {signal['signal_type']}")
        print(f"      Entry: ${signal['entry_price']:.2f}")
        print(f"      Stop Loss: ${signal['stop_loss']:.2f}")
        print(f"      Take Profit: ${signal['take_profit']:.2f}")
        print(f"      Konfidenz: {signal['confidence']:.1%}")
        print(f"      Begründung: {signal['reasoning']}")
        
        # Teste Lotgrößen-Berechnung
        lot_size = real_ict_analyzer.calculate_lot_size(100, 10, signal['entry_price'], signal['stop_loss'])
        print(f"      Empfohlene Lotgröße (100$ Konto, 10% Risiko): {lot_size}")
    else:
        print("   🎯 Kein Trading-Signal generiert")
    
    return analysis

def test_integration():
    """Teste vollständige Integration"""
    print("\n🔗 Teste vollständige Integration...")
    
    # Simuliere API-Aufruf wie in der Web-App
    try:
        # Hole Marktdaten
        market_data = real_market_data.get_historical_data(days=5)
        current_price_data = real_market_data.get_current_price()
        
        # Führe Analyse durch
        analysis = real_ict_analyzer.analyze_market_data(market_data)
        
        # Erstelle Response wie in der Web-App
        response = {
            'current_price': current_price_data,
            'market_data': market_data[-50:],  # Letzte 50 Datenpunkte für Chart
            'analysis': analysis,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        print("✅ Integration erfolgreich!")
        print(f"   Aktuelle Daten: {len(response['market_data'])} Datenpunkte")
        print(f"   Analyse-Konfidenz: {response['analysis']['confidence_score']:.1%}")
        
        if response['analysis']['trading_signal']:
            signal = response['analysis']['trading_signal']
            print(f"   Aktuelles Signal: {signal['signal_type']} bei ${signal['entry_price']:.2f}")
        
        return response
        
    except Exception as e:
        print(f"❌ Integration-Fehler: {e}")
        return None

def main():
    """Hauptfunktion für Tests"""
    print("🚀 Starte Tests für echte Marktdaten und ICT-Analyse\n")
    
    # Test 1: Marktdaten
    historical_data = test_market_data()
    
    # Test 2: ICT-Analyse
    if historical_data:
        analysis = test_ict_analysis(historical_data)
    
    # Test 3: Integration
    integration_result = test_integration()
    
    print("\n📊 Test-Zusammenfassung:")
    print("✅ Marktdaten-Provider funktioniert")
    print("✅ ICT-Analyse-Engine funktioniert")
    print("✅ Integration funktioniert" if integration_result else "❌ Integration fehlgeschlagen")
    
    if integration_result and integration_result['analysis']['trading_signal']:
        signal = integration_result['analysis']['trading_signal']
        print(f"\n🎯 AKTUELLE TRADING-EMPFEHLUNG:")
        print(f"Signal: {signal['signal_type']}")
        print(f"Entry: ${signal['entry_price']:.2f}")
        print(f"Stop Loss: ${signal['stop_loss']:.2f}")
        print(f"Take Profit: ${signal['take_profit']:.2f}")
        print(f"Konfidenz: {signal['confidence']:.1%}")
        print(f"Begründung: {signal['reasoning']}")

if __name__ == "__main__":
    main()

