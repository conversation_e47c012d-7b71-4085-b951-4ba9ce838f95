import math
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class SwingPoint:
    price: float
    timestamp: str
    index: int
    point_type: str  # 'high', 'low'
    strength: float  # 0.0 to 1.0
    confirmed: bool = False

@dataclass
class FairValueGap:
    start_price: float
    end_price: float
    timestamp: str
    gap_type: str  # 'bullish', 'bearish'
    gap_size: float
    strength: float
    filled: bool = False
    fill_percentage: float = 0.0

@dataclass
class LiquidityPool:
    price: float
    timestamp: str
    pool_type: str  # 'buy_stops', 'sell_stops'
    strength: float
    volume_estimate: float
    swept: bool = False

class EnhancedICTAnalyzer:
    """
    Verbesserte ICT-Analyse-Engine mit:
    - Optimierten Parametern für Gold-Trading
    - Erweiterten Algorithmen für bessere Genauigkeit
    - Verbesserter Konfidenz-Berechnung
    - Adaptive Schwellenwerte
    """
    
    def __init__(self):
        # Optimierte Parameter für Gold (XAUUSD)
        self.min_gap_size = 2.0  # Minimum Gap-Größe in USD (reduziert für bessere Erkennung)
        self.liquidity_threshold = 0.5  # Reduziert für mehr Sensitivität
        self.structure_lookback = 30  # Erhöht für bessere Strukturerkennung
        self.swing_window = 3  # Reduziert für mehr Swing-Points
        self.trend_confirmation_periods = 5  # Bestätigung für Trend-Änderungen
        
        # Adaptive Thresholds basierend auf Volatilität
        self.volatility_multiplier = 1.5
        self.min_confidence_threshold = 0.6  # Erhöht für bessere Signale
        
        # Gewichtungen für Signal-Berechnung
        self.weights = {
            'market_structure': 0.35,
            'fair_value_gaps': 0.25,
            'liquidity_pools': 0.20,
            'premium_discount': 0.15,
            'order_blocks': 0.05
        }
    
    def analyze_market_data(self, ohlc_data: List[Dict]) -> Dict[str, Any]:
        """Hauptanalyse-Funktion mit verbesserter Logik"""
        if not ohlc_data or len(ohlc_data) < 20:
            return self._get_empty_analysis()
        
        # Verarbeite und validiere Daten
        processed_data = self._process_and_validate_data(ohlc_data)
        if len(processed_data) < 20:
            return self._get_empty_analysis()
        
        # Berechne Markt-Volatilität für adaptive Schwellenwerte
        volatility = self._calculate_market_volatility(processed_data)
        self._adjust_thresholds_for_volatility(volatility)
        
        # Führe alle ICT-Analysen durch
        market_structure = self._analyze_enhanced_market_structure(processed_data)
        fair_value_gaps = self._identify_enhanced_fair_value_gaps(processed_data)
        liquidity_pools = self._identify_enhanced_liquidity_pools(processed_data)
        premium_discount = self._analyze_enhanced_premium_discount(processed_data)
        order_blocks = self._identify_order_blocks(processed_data)
        
        # Generiere Trading-Signal mit verbesserter Logik
        trading_signal = self._generate_enhanced_trading_signal(
            market_structure, fair_value_gaps, liquidity_pools, 
            premium_discount, order_blocks, processed_data
        )
        
        # Berechne Gesamt-Konfidenz mit gewichteten Faktoren
        confidence_score = self._calculate_weighted_confidence(
            market_structure, fair_value_gaps, liquidity_pools, 
            premium_discount, order_blocks
        )
        
        return {
            'market_structure': market_structure,
            'fair_value_gaps': fair_value_gaps,
            'liquidity_pools': liquidity_pools,
            'premium_discount': premium_discount,
            'order_blocks': order_blocks,
            'trading_signal': trading_signal,
            'confidence_score': confidence_score,
            'market_volatility': volatility,
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'analyzer_version': '2.0_enhanced'
        }
    
    def _process_and_validate_data(self, ohlc_data: List[Dict]) -> List[Dict]:
        """Verarbeite und validiere OHLC-Daten"""
        processed = []
        
        for i, candle in enumerate(ohlc_data):
            try:
                # Validiere OHLC-Logik
                open_price = float(candle.get('open', 0))
                high_price = float(candle.get('high', 0))
                low_price = float(candle.get('low', 0))
                close_price = float(candle.get('close', 0))
                
                # Grundlegende Validierung
                if (high_price >= max(open_price, close_price) and 
                    low_price <= min(open_price, close_price) and
                    high_price > 0 and low_price > 0):
                    
                    processed.append({
                        'timestamp': candle.get('timestamp', ''),
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': int(candle.get('volume', 0)),
                        'index': i
                    })
            except (ValueError, TypeError) as e:
                logger.warning(f"Invalid candle data at index {i}: {e}")
                continue
        
        return processed
    
    def _calculate_market_volatility(self, data: List[Dict]) -> float:
        """Berechne Markt-Volatilität für adaptive Schwellenwerte"""
        if len(data) < 10:
            return 0.01  # Default Volatilität
        
        # Berechne True Range für die letzten 20 Kerzen
        recent_data = data[-20:]
        true_ranges = []
        
        for i in range(1, len(recent_data)):
            current = recent_data[i]
            previous = recent_data[i-1]
            
            tr1 = current['high'] - current['low']
            tr2 = abs(current['high'] - previous['close'])
            tr3 = abs(current['low'] - previous['close'])
            
            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)
        
        # Average True Range
        atr = sum(true_ranges) / len(true_ranges) if true_ranges else 0.01
        
        # Normalisiere basierend auf Preis
        current_price = data[-1]['close']
        volatility = atr / current_price if current_price > 0 else 0.01
        
        return min(max(volatility, 0.001), 0.05)  # Begrenze zwischen 0.1% und 5%
    
    def _adjust_thresholds_for_volatility(self, volatility: float):
        """Passe Schwellenwerte basierend auf Markt-Volatilität an"""
        # Höhere Volatilität = höhere Schwellenwerte
        volatility_factor = max(0.5, min(2.0, volatility * 100))
        
        self.min_gap_size = 2.0 * volatility_factor
        self.liquidity_threshold = 0.5 * volatility_factor
        
        logger.info(f"Adjusted thresholds for volatility {volatility:.4f}: gap_size={self.min_gap_size:.2f}, liquidity={self.liquidity_threshold:.2f}")
    
    def _analyze_enhanced_market_structure(self, data: List[Dict]) -> Dict[str, Any]:
        """Verbesserte Marktstruktur-Analyse"""
        if len(data) < self.structure_lookback:
            return {'trend': 'sideways', 'strength': 0.5, 'confidence': 0.3}
        
        # Finde Swing-Points mit verbessertem Algorithmus
        swing_highs = self._find_enhanced_swing_points(data, 'high')
        swing_lows = self._find_enhanced_swing_points(data, 'low')
        
        # Analysiere Struktur-Sequenz
        structure_analysis = self._analyze_structure_sequence(swing_highs, swing_lows)
        
        # Bestimme Trend mit Konfidenz
        trend_analysis = self._determine_trend_with_confidence(structure_analysis, data)
        
        # Identifiziere Struktur-Brüche
        structure_breaks = self._identify_structure_breaks(swing_highs, swing_lows, data)
        
        return {
            'trend': trend_analysis['trend'],
            'strength': trend_analysis['strength'],
            'confidence': trend_analysis['confidence'],
            'swing_highs': [self._swing_point_to_dict(sp) for sp in swing_highs[-5:]],
            'swing_lows': [self._swing_point_to_dict(sp) for sp in swing_lows[-5:]],
            'structure_breaks': structure_breaks,
            'last_structure_change': self._get_last_structure_change(structure_breaks),
            'trend_change_probability': self._calculate_trend_change_probability(structure_analysis, data)
        }
    
    def _find_enhanced_swing_points(self, data: List[Dict], point_type: str) -> List[SwingPoint]:
        """Verbesserte Swing-Point-Erkennung"""
        swing_points = []
        price_key = 'high' if point_type == 'high' else 'low'
        comparison = max if point_type == 'high' else min
        
        for i in range(self.swing_window, len(data) - self.swing_window):
            current_price = data[i][price_key]
            
            # Prüfe ob es ein lokales Extremum ist
            window_prices = [data[j][price_key] for j in range(i - self.swing_window, i + self.swing_window + 1)]
            
            if comparison(window_prices) == current_price:
                # Berechne Stärke basierend auf Umgebung
                strength = self._calculate_swing_strength(data, i, point_type)
                
                swing_point = SwingPoint(
                    price=current_price,
                    timestamp=data[i]['timestamp'],
                    index=i,
                    point_type=point_type,
                    strength=strength,
                    confirmed=i < len(data) - self.trend_confirmation_periods
                )
                
                swing_points.append(swing_point)
        
        return swing_points
    
    def _calculate_swing_strength(self, data: List[Dict], index: int, point_type: str) -> float:
        """Berechne Stärke eines Swing-Points"""
        if index < 5 or index >= len(data) - 5:
            return 0.5
        
        price_key = 'high' if point_type == 'high' else 'low'
        current_price = data[index][price_key]
        
        # Berechne Abstand zu umgebenden Preisen
        surrounding_prices = []
        for i in range(max(0, index - 10), min(len(data), index + 10)):
            if i != index:
                surrounding_prices.append(data[i][price_key])
        
        if not surrounding_prices:
            return 0.5
        
        if point_type == 'high':
            # Für Highs: Je höher im Vergleich zur Umgebung, desto stärker
            avg_surrounding = sum(surrounding_prices) / len(surrounding_prices)
            strength = min(1.0, (current_price - avg_surrounding) / avg_surrounding * 10)
        else:
            # Für Lows: Je niedriger im Vergleich zur Umgebung, desto stärker
            avg_surrounding = sum(surrounding_prices) / len(surrounding_prices)
            strength = min(1.0, (avg_surrounding - current_price) / avg_surrounding * 10)
        
        return max(0.1, strength)
    
    def _swing_point_to_dict(self, swing_point: SwingPoint) -> Dict:
        """Konvertiere SwingPoint zu Dictionary"""
        return {
            'price': swing_point.price,
            'timestamp': swing_point.timestamp,
            'type': swing_point.point_type,
            'strength': swing_point.strength,
            'confirmed': swing_point.confirmed
        }

    def _analyze_structure_sequence(self, highs: List[SwingPoint], lows: List[SwingPoint]) -> Dict:
        """Analysiere Sequenz von Swing-Points für Trend-Bestimmung"""
        if len(highs) < 2 or len(lows) < 2:
            return {'higher_highs': 0, 'higher_lows': 0, 'lower_highs': 0, 'lower_lows': 0}

        # Zähle Higher Highs/Lower Highs
        higher_highs = sum(1 for i in range(1, len(highs)) if highs[i].price > highs[i-1].price)
        lower_highs = sum(1 for i in range(1, len(highs)) if highs[i].price < highs[i-1].price)

        # Zähle Higher Lows/Lower Lows
        higher_lows = sum(1 for i in range(1, len(lows)) if lows[i].price > lows[i-1].price)
        lower_lows = sum(1 for i in range(1, len(lows)) if lows[i].price < lows[i-1].price)

        return {
            'higher_highs': higher_highs,
            'higher_lows': higher_lows,
            'lower_highs': lower_highs,
            'lower_lows': lower_lows
        }

    def _determine_trend_with_confidence(self, structure: Dict, data: List[Dict]) -> Dict:
        """Bestimme Trend mit Konfidenz-Score"""
        hh = structure['higher_highs']
        hl = structure['higher_lows']
        lh = structure['lower_highs']
        ll = structure['lower_lows']

        total_signals = hh + hl + lh + ll
        if total_signals == 0:
            return {'trend': 'sideways', 'strength': 0.5, 'confidence': 0.3}

        # Bullische Signale
        bullish_score = (hh + hl) / total_signals

        # Bestimme Trend
        if bullish_score > 0.65:
            trend = 'bullish'
            strength = bullish_score
        elif bullish_score < 0.35:
            trend = 'bearish'
            strength = 1 - bullish_score
        else:
            trend = 'sideways'
            strength = 0.5

        # Berechne Konfidenz basierend auf Klarheit der Signale
        confidence = abs(bullish_score - 0.5) * 2  # 0.0 bis 1.0

        # Zusätzliche Konfidenz durch Preis-Momentum
        momentum_confidence = self._calculate_momentum_confidence(data)
        final_confidence = (confidence + momentum_confidence) / 2

        return {
            'trend': trend,
            'strength': strength,
            'confidence': min(1.0, final_confidence)
        }

    def _calculate_momentum_confidence(self, data: List[Dict]) -> float:
        """Berechne Konfidenz basierend auf Preis-Momentum"""
        if len(data) < 10:
            return 0.5

        recent_data = data[-10:]
        price_changes = []

        for i in range(1, len(recent_data)):
            change = (recent_data[i]['close'] - recent_data[i-1]['close']) / recent_data[i-1]['close']
            price_changes.append(change)

        if not price_changes:
            return 0.5

        # Berechne durchschnittliche Richtung
        avg_change = sum(price_changes) / len(price_changes)

        # Berechne Konsistenz der Richtung
        positive_changes = sum(1 for change in price_changes if change > 0)
        consistency = abs(positive_changes / len(price_changes) - 0.5) * 2

        # Kombiniere Stärke und Konsistenz
        momentum_strength = abs(avg_change) * 100  # Normalisiere
        confidence = min(1.0, (consistency + momentum_strength) / 2)

        return confidence

    def _identify_structure_breaks(self, highs: List[SwingPoint], lows: List[SwingPoint], data: List[Dict]) -> List[Dict]:
        """Identifiziere Struktur-Brüche"""
        breaks = []
        current_price = data[-1]['close']

        # Prüfe auf Break of Structure (BOS)
        if len(highs) >= 2:
            last_high = highs[-1]
            prev_high = highs[-2]

            if current_price > last_high.price and last_high.price > prev_high.price:
                breaks.append({
                    'type': 'bullish_bos',
                    'price': last_high.price,
                    'timestamp': last_high.timestamp,
                    'strength': last_high.strength
                })

        if len(lows) >= 2:
            last_low = lows[-1]
            prev_low = lows[-2]

            if current_price < last_low.price and last_low.price < prev_low.price:
                breaks.append({
                    'type': 'bearish_bos',
                    'price': last_low.price,
                    'timestamp': last_low.timestamp,
                    'strength': last_low.strength
                })

        return breaks

    def _get_last_structure_change(self, structure_breaks: List[Dict]) -> Optional[Dict]:
        """Hole letzten Struktur-Wechsel"""
        if not structure_breaks:
            return None

        return structure_breaks[-1]

    def _calculate_trend_change_probability(self, structure: Dict, data: List[Dict]) -> float:
        """Berechne Wahrscheinlichkeit für Trend-Wechsel"""
        # Basis-Wahrscheinlichkeit
        base_probability = 0.3

        # Erhöhe Wahrscheinlichkeit bei gemischten Signalen
        total_signals = sum(structure.values())
        if total_signals > 0:
            bullish_ratio = (structure['higher_highs'] + structure['higher_lows']) / total_signals
            # Je näher an 0.5, desto höher die Wechsel-Wahrscheinlichkeit
            uncertainty = 1 - abs(bullish_ratio - 0.5) * 2
            base_probability += uncertainty * 0.4

        # Berücksichtige Volatilität
        volatility = self._calculate_market_volatility(data)
        volatility_factor = min(1.0, volatility * 20)  # Normalisiere

        final_probability = min(0.8, base_probability + volatility_factor * 0.2)
        return final_probability

    def _identify_enhanced_fair_value_gaps(self, data: List[Dict]) -> List[Dict]:
        """Verbesserte Fair Value Gap Erkennung"""
        gaps = []

        for i in range(2, len(data)):
            prev_candle = data[i-2]
            current_candle = data[i-1]
            next_candle = data[i]

            # Bullish FVG: Gap zwischen prev high und next low
            if (prev_candle['high'] < next_candle['low'] and
                next_candle['low'] - prev_candle['high'] >= self.min_gap_size):

                gap_size = next_candle['low'] - prev_candle['high']
                strength = self._calculate_gap_strength(prev_candle, current_candle, next_candle, 'bullish')

                gaps.append({
                    'type': 'bullish',
                    'start_price': prev_candle['high'],
                    'end_price': next_candle['low'],
                    'gap_size': gap_size,
                    'timestamp': current_candle['timestamp'],
                    'strength': strength,
                    'filled': self._is_gap_filled(data[i:], prev_candle['high'], next_candle['low']),
                    'fill_percentage': self._calculate_gap_fill_percentage(data[i:], prev_candle['high'], next_candle['low'])
                })

            # Bearish FVG: Gap zwischen prev low und next high
            elif (prev_candle['low'] > next_candle['high'] and
                  prev_candle['low'] - next_candle['high'] >= self.min_gap_size):

                gap_size = prev_candle['low'] - next_candle['high']
                strength = self._calculate_gap_strength(prev_candle, current_candle, next_candle, 'bearish')

                gaps.append({
                    'type': 'bearish',
                    'start_price': prev_candle['low'],
                    'end_price': next_candle['high'],
                    'gap_size': gap_size,
                    'timestamp': current_candle['timestamp'],
                    'strength': strength,
                    'filled': self._is_gap_filled(data[i:], next_candle['high'], prev_candle['low']),
                    'fill_percentage': self._calculate_gap_fill_percentage(data[i:], next_candle['high'], prev_candle['low'])
                })

        # Sortiere nach Stärke und behalte nur die besten
        gaps.sort(key=lambda x: x['strength'], reverse=True)
        return gaps[:10]  # Top 10 Gaps

    def _calculate_gap_strength(self, prev_candle: Dict, current_candle: Dict, next_candle: Dict, gap_type: str) -> float:
        """Berechne Stärke eines Fair Value Gaps"""
        # Basis-Stärke basierend auf Gap-Größe
        if gap_type == 'bullish':
            gap_size = next_candle['low'] - prev_candle['high']
        else:
            gap_size = prev_candle['low'] - next_candle['high']

        # Normalisiere Gap-Größe
        avg_price = (prev_candle['close'] + next_candle['close']) / 2
        relative_gap_size = gap_size / avg_price

        # Basis-Stärke (0.0 bis 1.0)
        base_strength = min(1.0, relative_gap_size * 200)  # 0.5% Gap = 1.0 Stärke

        # Berücksichtige Volumen des mittleren Candles
        volume_factor = min(2.0, current_candle.get('volume', 1000) / 1000)

        # Berücksichtige Candle-Größe
        candle_size = abs(current_candle['close'] - current_candle['open'])
        candle_factor = min(2.0, candle_size / avg_price * 100)

        # Kombiniere Faktoren
        final_strength = base_strength * (1 + volume_factor * 0.2 + candle_factor * 0.1)
        return min(1.0, final_strength)

    def _is_gap_filled(self, future_data: List[Dict], low_price: float, high_price: float) -> bool:
        """Prüfe ob Gap gefüllt wurde"""
        for candle in future_data:
            if candle['low'] <= low_price and candle['high'] >= high_price:
                return True
        return False

    def _calculate_gap_fill_percentage(self, future_data: List[Dict], low_price: float, high_price: float) -> float:
        """Berechne wie viel Prozent des Gaps gefüllt wurden"""
        gap_size = high_price - low_price
        if gap_size <= 0:
            return 0.0

        max_penetration = 0.0
        for candle in future_data:
            if candle['low'] <= high_price and candle['high'] >= low_price:
                # Gap wurde berührt
                penetration_low = max(0, low_price - candle['low'])
                penetration_high = max(0, candle['high'] - high_price)
                total_penetration = penetration_low + penetration_high
                max_penetration = max(max_penetration, total_penetration)

        fill_percentage = min(1.0, max_penetration / gap_size)
        return fill_percentage

    def _identify_enhanced_liquidity_pools(self, data: List[Dict]) -> List[Dict]:
        """Verbesserte Liquiditätspools-Erkennung"""
        pools = []

        # Identifiziere potenzielle Stop-Loss-Cluster
        for i in range(10, len(data) - 5):
            current_candle = data[i]

            # Suche nach starken Bewegungen (Liquiditäts-Sweeps)
            price_move = abs(current_candle['close'] - current_candle['open'])
            avg_move = self._calculate_average_candle_size(data[i-10:i])

            if price_move > avg_move * self.liquidity_threshold:
                # Bestimme Pool-Typ basierend auf Bewegungsrichtung
                if current_candle['close'] > current_candle['open']:
                    # Bullische Bewegung - wahrscheinlich Sell-Stops getroffen
                    pool_type = 'sell_stops'
                    pool_price = current_candle['low']
                else:
                    # Bärische Bewegung - wahrscheinlich Buy-Stops getroffen
                    pool_type = 'buy_stops'
                    pool_price = current_candle['high']

                strength = min(1.0, price_move / avg_move / 3)  # Normalisiere
                volume_estimate = current_candle.get('volume', 1000) * strength

                pools.append({
                    'price': pool_price,
                    'timestamp': current_candle['timestamp'],
                    'type': pool_type,
                    'strength': strength,
                    'volume_estimate': volume_estimate,
                    'swept': self._check_if_pool_swept(data[i+1:], pool_price, pool_type)
                })

        # Sortiere nach Stärke
        pools.sort(key=lambda x: x['strength'], reverse=True)
        return pools[:8]  # Top 8 Pools

    def _calculate_average_candle_size(self, data: List[Dict]) -> float:
        """Berechne durchschnittliche Candle-Größe"""
        if not data:
            return 1.0

        sizes = [abs(candle['close'] - candle['open']) for candle in data]
        return sum(sizes) / len(sizes) if sizes else 1.0

    def _check_if_pool_swept(self, future_data: List[Dict], pool_price: float, pool_type: str) -> bool:
        """Prüfe ob Liquiditätspool bereits abgeholt wurde"""
        for candle in future_data:
            if pool_type == 'buy_stops' and candle['high'] >= pool_price:
                return True
            elif pool_type == 'sell_stops' and candle['low'] <= pool_price:
                return True
        return False

    def _analyze_enhanced_premium_discount(self, data: List[Dict]) -> Dict[str, Any]:
        """Verbesserte Premium/Discount-Analyse"""
        if len(data) < 20:
            return {'zone': 'neutral', 'risk_level': 'medium', 'position': 0.5}

        # Verwende längeren Zeitraum für bessere Range-Bestimmung
        lookback_period = min(50, len(data))
        recent_data = data[-lookback_period:]

        # Berechne Range basierend auf Highs/Lows
        highs = [candle['high'] for candle in recent_data]
        lows = [candle['low'] for candle in recent_data]

        range_high = max(highs)
        range_low = min(lows)
        range_size = range_high - range_low

        current_price = data[-1]['close']

        # Berechne Position im Range
        if range_size == 0:
            position = 0.5
        else:
            position = (current_price - range_low) / range_size

        # Bestimme Zone mit verfeinerten Schwellenwerten
        if position >= 0.75:
            zone = 'premium'
            risk_level = 'high'
        elif position >= 0.6:
            zone = 'upper_neutral'
            risk_level = 'medium_high'
        elif position <= 0.25:
            zone = 'discount'
            risk_level = 'low'
        elif position <= 0.4:
            zone = 'lower_neutral'
            risk_level = 'medium_low'
        else:
            zone = 'equilibrium'
            risk_level = 'medium'

        # Berechne zusätzliche Metriken
        price_momentum = self._calculate_price_momentum(data[-10:])

        return {
            'zone': zone,
            'risk_level': risk_level,
            'position': round(position, 3),
            'range_high': range_high,
            'range_low': range_low,
            'range_size': range_size,
            'current_price': current_price,
            'price_momentum': price_momentum,
            'recommendation': self._get_zone_recommendation(zone, price_momentum)
        }

    def _calculate_price_momentum(self, data: List[Dict]) -> str:
        """Berechne Preis-Momentum"""
        if len(data) < 3:
            return 'neutral'

        price_changes = []
        for i in range(1, len(data)):
            change = data[i]['close'] - data[i-1]['close']
            price_changes.append(change)

        avg_change = sum(price_changes) / len(price_changes)

        if avg_change > 0.5:
            return 'strong_bullish'
        elif avg_change > 0.1:
            return 'bullish'
        elif avg_change < -0.5:
            return 'strong_bearish'
        elif avg_change < -0.1:
            return 'bearish'
        else:
            return 'neutral'

    def _get_zone_recommendation(self, zone: str, momentum: str) -> str:
        """Hole Empfehlung basierend auf Zone und Momentum"""
        if zone == 'premium':
            return 'consider_sells' if momentum in ['bearish', 'strong_bearish'] else 'wait'
        elif zone == 'discount':
            return 'consider_buys' if momentum in ['bullish', 'strong_bullish'] else 'wait'
        else:
            return 'neutral'

    def _identify_order_blocks(self, data: List[Dict]) -> List[Dict]:
        """Identifiziere Order Blocks (institutionelle Aufträge)"""
        order_blocks = []

        for i in range(10, len(data) - 5):
            current = data[i]

            # Suche nach starken Bewegungen (potenzielle institutionelle Aktivität)
            candle_size = abs(current['close'] - current['open'])
            avg_size = self._calculate_average_candle_size(data[i-10:i])

            if candle_size > avg_size * 2:  # Starke Bewegung
                # Bestimme Order Block Typ
                if current['close'] > current['open']:
                    # Bullischer Order Block
                    ob_type = 'bullish'
                    ob_price = current['low']
                else:
                    # Bärischer Order Block
                    ob_type = 'bearish'
                    ob_price = current['high']

                strength = min(1.0, candle_size / avg_size / 4)

                order_blocks.append({
                    'type': ob_type,
                    'price': ob_price,
                    'timestamp': current['timestamp'],
                    'strength': strength,
                    'tested': self._check_if_level_tested(data[i+1:], ob_price),
                    'candle_size': candle_size
                })

        return order_blocks[-5:]  # Letzte 5 Order Blocks

    def _check_if_level_tested(self, future_data: List[Dict], level_price: float) -> bool:
        """Prüfe ob Level getestet wurde"""
        tolerance = 2.0  # 2 USD Toleranz

        for candle in future_data:
            if (candle['low'] <= level_price + tolerance and
                candle['high'] >= level_price - tolerance):
                return True
        return False

    def _generate_enhanced_trading_signal(self, market_structure: Dict, fair_value_gaps: List[Dict],
                                        liquidity_pools: List[Dict], premium_discount: Dict,
                                        order_blocks: List[Dict], data: List[Dict]) -> Optional[Dict]:
        """Generiere Trading-Signal mit verbesserter Logik"""
        if not data:
            return None

        current_price = data[-1]['close']
        signal_strength = 0.0
        reasoning = []

        # 1. Market Structure Bewertung (35% Gewichtung)
        structure_score = 0.0
        if market_structure['trend'] == 'bullish':
            structure_score = market_structure['strength'] * market_structure['confidence']
            reasoning.append(f"Bullische Marktstruktur (Stärke: {structure_score:.2f})")
        elif market_structure['trend'] == 'bearish':
            structure_score = -market_structure['strength'] * market_structure['confidence']
            reasoning.append(f"Bärische Marktstruktur (Stärke: {abs(structure_score):.2f})")

        signal_strength += structure_score * self.weights['market_structure']

        # 2. Fair Value Gaps Bewertung (25% Gewichtung)
        fvg_score = 0.0
        active_gaps = [gap for gap in fair_value_gaps if not gap['filled']]

        for gap in active_gaps[:3]:  # Top 3 aktive Gaps
            gap_distance = abs(current_price - (gap['start_price'] + gap['end_price']) / 2)
            if gap_distance < 10.0:  # Nahe genug zum Gap
                if gap['type'] == 'bullish' and current_price >= gap['start_price']:
                    fvg_score += gap['strength'] * 0.5
                    reasoning.append(f"Bullish FVG bei {gap['start_price']:.2f}")
                elif gap['type'] == 'bearish' and current_price <= gap['start_price']:
                    fvg_score -= gap['strength'] * 0.5
                    reasoning.append(f"Bearish FVG bei {gap['start_price']:.2f}")

        signal_strength += fvg_score * self.weights['fair_value_gaps']

        # 3. Premium/Discount Zone Bewertung (15% Gewichtung)
        pd_score = 0.0
        if premium_discount['zone'] == 'discount':
            pd_score = 0.5
            reasoning.append("Preis in Discount-Zone")
        elif premium_discount['zone'] == 'premium':
            pd_score = -0.5
            reasoning.append("Preis in Premium-Zone")

        signal_strength += pd_score * self.weights['premium_discount']

        # 4. Liquiditätspools Bewertung (20% Gewichtung)
        liq_score = 0.0
        recent_pools = [pool for pool in liquidity_pools if not pool['swept']]

        for pool in recent_pools[:2]:  # Top 2 unberührte Pools
            pool_distance = abs(current_price - pool['price'])
            if pool_distance < 5.0:  # Nahe genug zum Pool
                if pool['type'] == 'sell_stops':
                    liq_score -= pool['strength'] * 0.3
                    reasoning.append(f"Sell-Stops bei {pool['price']:.2f} wurden getroffen")
                elif pool['type'] == 'buy_stops':
                    liq_score += pool['strength'] * 0.3
                    reasoning.append(f"Buy-Stops bei {pool['price']:.2f} wurden getroffen")

        signal_strength += liq_score * self.weights['liquidity_pools']

        # 5. Order Blocks Bewertung (5% Gewichtung)
        ob_score = 0.0
        for ob in order_blocks[-2:]:  # Letzte 2 Order Blocks
            ob_distance = abs(current_price - ob['price'])
            if ob_distance < 3.0 and not ob['tested']:  # Nahe und ungetestet
                if ob['type'] == 'bullish':
                    ob_score += ob['strength'] * 0.2
                    reasoning.append(f"Bullish Order Block bei {ob['price']:.2f}")
                elif ob['type'] == 'bearish':
                    ob_score -= ob['strength'] * 0.2
                    reasoning.append(f"Bearish Order Block bei {ob['price']:.2f}")

        signal_strength += ob_score * self.weights['order_blocks']

        # Signal generieren wenn Stärke ausreichend
        if abs(signal_strength) >= self.min_confidence_threshold:
            signal_type = 'BUY' if signal_strength > 0 else 'SELL'

            # Berechne Entry, SL und TP mit verbesserter Logik
            entry_price = current_price
            stop_loss, take_profit = self._calculate_enhanced_levels(
                signal_type, entry_price, market_structure, data
            )

            # Berechne finale Konfidenz
            confidence = min(1.0, abs(signal_strength))

            return {
                'signal_type': signal_type,
                'entry_price': round(entry_price, 2),
                'stop_loss': round(stop_loss, 2),
                'take_profit': round(take_profit, 2),
                'confidence': round(confidence, 3),
                'reasoning': '; '.join(reasoning),
                'risk_reward_ratio': round(abs(take_profit - entry_price) / abs(entry_price - stop_loss), 2),
                'signal_strength': round(signal_strength, 3)
            }

        return None

    def _calculate_enhanced_levels(self, signal_type: str, entry_price: float,
                                 market_structure: Dict, data: List[Dict]) -> Tuple[float, float]:
        """Berechne Stop-Loss und Take-Profit mit verbesserter Logik"""
        atr = self._calculate_atr(data, 14)

        if signal_type == 'BUY':
            # Für Buy: SL unter letztem Swing-Low oder ATR-basiert
            swing_lows = market_structure.get('swing_lows', [])
            if swing_lows:
                recent_low = min([sl['price'] for sl in swing_lows[-2:]])
                stop_loss = min(recent_low - 1.0, entry_price - atr * 1.5)
            else:
                stop_loss = entry_price - atr * 2.0

            # TP basierend auf 1:1.5 RR
            risk_distance = entry_price - stop_loss
            take_profit = entry_price + risk_distance * 1.5

        else:  # SELL
            # Für Sell: SL über letztem Swing-High oder ATR-basiert
            swing_highs = market_structure.get('swing_highs', [])
            if swing_highs:
                recent_high = max([sh['price'] for sh in swing_highs[-2:]])
                stop_loss = max(recent_high + 1.0, entry_price + atr * 1.5)
            else:
                stop_loss = entry_price + atr * 2.0

            # TP basierend auf 1:1.5 RR
            risk_distance = stop_loss - entry_price
            take_profit = entry_price - risk_distance * 1.5

        return stop_loss, take_profit

    def _calculate_atr(self, data: List[Dict], period: int = 14) -> float:
        """Berechne Average True Range"""
        if len(data) < period + 1:
            return 5.0  # Default ATR für Gold

        true_ranges = []
        for i in range(1, min(len(data), period + 1)):
            current = data[-i]
            previous = data[-i-1]

            tr1 = current['high'] - current['low']
            tr2 = abs(current['high'] - previous['close'])
            tr3 = abs(current['low'] - previous['close'])

            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)

        return sum(true_ranges) / len(true_ranges) if true_ranges else 5.0

    def _calculate_weighted_confidence(self, market_structure: Dict, fair_value_gaps: List[Dict],
                                     liquidity_pools: List[Dict], premium_discount: Dict,
                                     order_blocks: List[Dict]) -> float:
        """Berechne gewichtete Gesamt-Konfidenz"""
        confidence_scores = []

        # Market Structure Konfidenz
        structure_confidence = market_structure.get('confidence', 0.5)
        confidence_scores.append(structure_confidence * self.weights['market_structure'])

        # Fair Value Gaps Konfidenz
        if fair_value_gaps:
            avg_gap_strength = sum(gap['strength'] for gap in fair_value_gaps[:3]) / min(3, len(fair_value_gaps))
            fvg_confidence = min(1.0, avg_gap_strength)
        else:
            fvg_confidence = 0.3
        confidence_scores.append(fvg_confidence * self.weights['fair_value_gaps'])

        # Liquidity Pools Konfidenz
        if liquidity_pools:
            avg_pool_strength = sum(pool['strength'] for pool in liquidity_pools[:3]) / min(3, len(liquidity_pools))
            liq_confidence = min(1.0, avg_pool_strength)
        else:
            liq_confidence = 0.3
        confidence_scores.append(liq_confidence * self.weights['liquidity_pools'])

        # Premium/Discount Konfidenz
        pd_confidence = 0.8 if premium_discount['zone'] in ['premium', 'discount'] else 0.5
        confidence_scores.append(pd_confidence * self.weights['premium_discount'])

        # Order Blocks Konfidenz
        if order_blocks:
            avg_ob_strength = sum(ob['strength'] for ob in order_blocks) / len(order_blocks)
            ob_confidence = min(1.0, avg_ob_strength)
        else:
            ob_confidence = 0.3
        confidence_scores.append(ob_confidence * self.weights['order_blocks'])

        # Berechne gewichtete Gesamt-Konfidenz
        total_confidence = sum(confidence_scores)

        # Bonus für Übereinstimmung zwischen Indikatoren
        alignment_bonus = self._calculate_alignment_bonus(
            market_structure, fair_value_gaps, liquidity_pools, premium_discount
        )

        final_confidence = min(1.0, total_confidence + alignment_bonus)
        return round(final_confidence, 3)

    def _calculate_alignment_bonus(self, market_structure: Dict, fair_value_gaps: List[Dict],
                                 liquidity_pools: List[Dict], premium_discount: Dict) -> float:
        """Berechne Bonus für Übereinstimmung zwischen Indikatoren"""
        alignment_score = 0.0

        trend = market_structure.get('trend', 'sideways')

        # Prüfe Übereinstimmung zwischen Trend und Premium/Discount
        if trend == 'bullish' and premium_discount['zone'] in ['discount', 'lower_neutral']:
            alignment_score += 0.1
        elif trend == 'bearish' and premium_discount['zone'] in ['premium', 'upper_neutral']:
            alignment_score += 0.1

        # Prüfe Übereinstimmung zwischen Trend und FVGs
        bullish_gaps = sum(1 for gap in fair_value_gaps if gap['type'] == 'bullish' and not gap['filled'])
        bearish_gaps = sum(1 for gap in fair_value_gaps if gap['type'] == 'bearish' and not gap['filled'])

        if trend == 'bullish' and bullish_gaps > bearish_gaps:
            alignment_score += 0.05
        elif trend == 'bearish' and bearish_gaps > bullish_gaps:
            alignment_score += 0.05

        # Prüfe Übereinstimmung zwischen Trend und Liquiditätspools
        buy_stop_pools = sum(1 for pool in liquidity_pools if pool['type'] == 'buy_stops' and not pool['swept'])
        sell_stop_pools = sum(1 for pool in liquidity_pools if pool['type'] == 'sell_stops' and not pool['swept'])

        if trend == 'bullish' and sell_stop_pools > buy_stop_pools:
            alignment_score += 0.05
        elif trend == 'bearish' and buy_stop_pools > sell_stop_pools:
            alignment_score += 0.05

        return alignment_score

    def _get_empty_analysis(self) -> Dict[str, Any]:
        """Gibt leere Analyse zurück wenn nicht genug Daten vorhanden"""
        return {
            'market_structure': {
                'trend': 'sideways',
                'strength': 0.5,
                'confidence': 0.3,
                'swing_highs': [],
                'swing_lows': [],
                'structure_breaks': [],
                'last_structure_change': None,
                'trend_change_probability': 0.5
            },
            'fair_value_gaps': [],
            'liquidity_pools': [],
            'premium_discount': {
                'zone': 'neutral',
                'risk_level': 'medium',
                'position': 0.5,
                'recommendation': 'neutral'
            },
            'order_blocks': [],
            'trading_signal': None,
            'confidence_score': 0.3,
            'market_volatility': 0.01,
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'analyzer_version': '2.0_enhanced',
            'error': 'Insufficient data for analysis'
        }

    def calculate_lot_size(self, account_balance: float, risk_percentage: float,
                          entry_price: float, stop_loss: float) -> float:
        """Berechne optimale Lotgröße basierend auf Risikomanagement"""
        if entry_price <= 0 or stop_loss <= 0 or account_balance <= 0:
            return 0.01  # Minimum Lot Size

        # Berechne Risiko-Betrag
        risk_amount = account_balance * (risk_percentage / 100)

        # Berechne Pip-Wert für Gold (XAUUSD)
        # 1 Lot Gold = 100 Unzen, 1 Pip = $0.01 pro Unze = $1 pro Lot
        pip_value = 1.0  # $1 pro Pip für 1 Standard-Lot

        # Berechne Pip-Distanz
        pip_distance = abs(entry_price - stop_loss)

        # Berechne Lotgröße
        if pip_distance > 0:
            lot_size = risk_amount / (pip_distance * pip_value)
        else:
            lot_size = 0.01

        # Begrenze Lotgröße
        lot_size = max(0.01, min(lot_size, 10.0))  # Zwischen 0.01 und 10 Lots

        # Runde auf 2 Dezimalstellen
        return round(lot_size, 2)
