<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Trading App - Professional ICT Analysis</title>
    <link rel="manifest" href="/static/manifest.json">
    <meta name="theme-color" content="#1e3c72">
    <link rel="icon" type="image/png" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🥇</text></svg>">
    
    <style>
        :root {
            --primary-color: #ffd700;
            --primary-dark: #e6c200;
            --secondary-color: #1e3c72;
            --secondary-light: #2a5298;
            --accent-color: #ff6b35;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
            --border-radius: 12px;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
            color: white;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 0.8s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, var(--primary-color), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 25px;
            border-radius: var(--border-radius);
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--box-shadow);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .controls-section {
            margin-bottom: 40px;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            color: var(--dark-color);
        }

        .btn-success {
            background: linear-gradient(45deg, var(--success-color), #20c997);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, var(--info-color), #20c997);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, var(--dark-color), #6c757d);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        .status-bar {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 15px 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            min-height: 60px;
        }

        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .chart-container, .signal-container {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 25px;
            border-radius: var(--border-radius);
            min-height: 400px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            font-size: 1.8rem;
        }

        .chart-canvas {
            width: 100%;
            height: 300px;
            border-radius: 8px;
            background: rgba(0,0,0,0.2);
        }

        .signal-waiting {
            text-align: center;
            padding: 60px 20px;
            opacity: 0.7;
        }

        .signal-waiting .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: spin 3s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .analysis-results {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 25px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            display: none;
            animation: slideInUp 0.5s ease-out;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .analysis-item {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
        }

        .analysis-item h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .signal-card {
            background: linear-gradient(135deg, rgba(220,53,69,0.2), rgba(220,53,69,0.1));
            border: 2px solid var(--danger-color);
            padding: 25px;
            border-radius: var(--border-radius);
            margin-top: 20px;
            display: none;
            animation: slideInUp 0.5s ease-out;
        }

        .signal-card.buy {
            background: linear-gradient(135deg, rgba(40,167,69,0.2), rgba(40,167,69,0.1));
            border-color: var(--success-color);
        }

        .signal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .signal-type {
            font-size: 2rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .confidence-badge {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .signal-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .signal-detail {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }

        .signal-detail strong {
            display: block;
            color: var(--primary-color);
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .execute-btn {
            width: 100%;
            padding: 15px;
            font-size: 1.1rem;
            margin-top: 20px;
        }

        .config-panel {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 25px;
            border-radius: var(--border-radius);
            margin-top: 20px;
            display: none;
            animation: slideInDown 0.5s ease-out;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .form-group input, .form-group select {
            padding: 12px;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255,215,0,0.2);
        }

        .footer {
            text-align: center;
            padding: 40px 20px;
            opacity: 0.7;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 40px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .signal-details {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading States */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Dark mode toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            z-index: 1000;
        }

        .theme-toggle:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="theme-toggle" onclick="toggleTheme()">
        🌙
    </div>

    <div class="container">
        <div class="header">
            <h1>🥇 Gold Trading App</h1>
            <p>Professionelle ICT-Analyse für XAUUSD mit automatischer Trade-Ausführung</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-value" id="current-price">$2000.00</span>
                <div class="stat-label">Aktueller Gold-Preis</div>
            </div>
            <div class="stat-card">
                <span class="stat-value" id="account-balance">$100.00</span>
                <div class="stat-label">Kontostand</div>
            </div>
            <div class="stat-card">
                <span class="stat-value" id="active-trades">0</span>
                <div class="stat-label">Aktive Trades</div>
            </div>
            <div class="stat-card">
                <span class="stat-value" id="total-pnl">$0.00</span>
                <div class="stat-label">Gesamt P&L</div>
            </div>
        </div>
        
        <div class="controls-section">
            <div class="controls-grid">
                <button class="btn btn-primary" onclick="runAnalysis()">
                    <span>🔍</span> ICT-Analyse starten
                </button>
                <button class="btn btn-success" onclick="runAutoAnalysis()">
                    <span>⚡</span> Auto-Analyse
                </button>
                <button class="btn btn-info" onclick="updateData()">
                    <span>🔄</span> Daten aktualisieren
                </button>
                <button class="btn btn-secondary" onclick="toggleConfig()">
                    <span>⚙️</span> Konfiguration
                </button>
            </div>
            
            <div class="status-bar" id="status-bar">
                <div class="status-icon"></div>
                <span id="status-text">Bereit für Trading-Analyse</span>
            </div>
        </div>
        
        <div class="config-panel" id="config-panel">
            <h3 class="section-title">
                <span class="section-icon">⚙️</span>
                Trading-Konfiguration
            </h3>
            <div class="config-grid">
                <div class="form-group">
                    <label for="account-balance-input">Kontostand ($)</label>
                    <input type="number" id="account-balance-input" value="100" min="1" max="100000">
                </div>
                <div class="form-group">
                    <label for="risk-percentage">Risiko (%)</label>
                    <input type="number" id="risk-percentage" value="10" min="1" max="50">
                </div>
                <div class="form-group">
                    <label for="auto-trading">Auto-Trading</label>
                    <select id="auto-trading">
                        <option value="enabled">Aktiviert</option>
                        <option value="disabled">Deaktiviert</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="risk-reward">Risk-Reward Ratio</label>
                    <select id="risk-reward">
                        <option value="1">1:1</option>
                        <option value="1.5" selected>1:1.5</option>
                        <option value="2">1:2</option>
                        <option value="3">1:3</option>
                    </select>
                </div>
            </div>
            <button class="btn btn-primary" onclick="saveConfig()">
                <span>💾</span> Konfiguration speichern
            </button>
        </div>
        
        <div class="main-content">
            <div class="chart-container">
                <h3 class="section-title">
                    <span class="section-icon">📈</span>
                    Live Chart
                </h3>
                <canvas id="chart" class="chart-canvas"></canvas>
            </div>
            
            <div class="signal-container">
                <h3 class="section-title">
                    <span class="section-icon">🎯</span>
                    Aktuelles Signal
                </h3>
                <div class="signal-waiting" id="signal-waiting">
                    <div class="icon">⏳</div>
                    <p>Warte auf Trading-Signal...</p>
                    <small>Führe eine ICT-Analyse durch, um Signale zu generieren</small>
                </div>
            </div>
        </div>
        
        <div class="analysis-results" id="analysis-results">
            <h3 class="section-title">
                <span class="section-icon">🧠</span>
                ICT-Analyse Ergebnisse
            </h3>
            <div class="analysis-grid" id="analysis-content">
                <!-- Analysis results will be populated here -->
            </div>
        </div>
        
        <div class="signal-card" id="signal-card">
            <div class="signal-header">
                <div class="signal-type" id="signal-type">BUY</div>
                <div class="confidence-badge" id="confidence-badge">85% Konfidenz</div>
            </div>
            <div class="signal-details" id="signal-details">
                <!-- Signal details will be populated here -->
            </div>
            <div id="signal-reasoning"></div>
            <button class="btn btn-success execute-btn" onclick="executeTrade()">
                <span>🚀</span> Trade ausführen
            </button>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 Gold Trading App - Powered by ICT Analysis</p>
            <p><small>Risikohinweis: Trading birgt Verlustrisiken. Handeln Sie verantwortungsvoll.</small></p>
        </div>
    </div>

    <script>
        let currentData = [];
        let chartContext = null;
        let autoAnalysisInterval = null;
        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            updateData();
            
            // Auto-update every 30 seconds
            setInterval(updateData, 30000);
        });
        
        function initializeChart() {
            const canvas = document.getElementById('chart');
            chartContext = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        
        async function updateData() {
            setStatus('📡 Lade Marktdaten...', 'loading');
            
            try {
                const response = await fetch('/api/market-data');
                currentData = await response.json();
                
                if (currentData.length > 0) {
                    const currentPrice = currentData[currentData.length - 1].close;
                    document.getElementById('current-price').textContent = '$' + currentPrice.toFixed(2);
                    drawChart();
                    setStatus('✅ Daten aktualisiert', 'success');
                    
                    // Add price change animation
                    animatePriceChange();
                }
            } catch (error) {
                setStatus('❌ Fehler beim Laden der Daten', 'error');
                console.error('Data update error:', error);
            }
        }
        
        function animatePriceChange() {
            const priceElement = document.getElementById('current-price');
            priceElement.style.transform = 'scale(1.1)';
            priceElement.style.color = 'var(--primary-color)';
            
            setTimeout(() => {
                priceElement.style.transform = 'scale(1)';
                priceElement.style.color = '';
            }, 300);
        }
        
        async function runAnalysis() {
            setStatus('🧠 Führe ICT-Analyse durch...', 'loading');
            
            try {
                const response = await fetch('/api/ict-analysis');
                const analysis = await response.json();
                
                showAnalysisResults(analysis);
                setStatus('✅ ICT-Analyse abgeschlossen', 'success');
                
                if (analysis.trading_signal) {
                    showTradingSignal(analysis.trading_signal);
                }
            } catch (error) {
                setStatus('❌ Fehler bei der Analyse', 'error');
                console.error('Analysis error:', error);
            }
        }
        
        async function runAutoAnalysis() {
            setStatus('⚡ Führe automatische Analyse durch...', 'loading');
            
            // Stop existing auto-analysis
            if (autoAnalysisInterval) {
                clearInterval(autoAnalysisInterval);
                autoAnalysisInterval = null;
                setStatus('⏹️ Auto-Analyse gestoppt', 'info');
                return;
            }
            
            // Start auto-analysis
            await runAnalysis();
            
            autoAnalysisInterval = setInterval(async () => {
                await runAnalysis();
            }, 60000); // Every minute
            
            setStatus('🔄 Auto-Analyse aktiv (alle 60s)', 'info');
        }
        
        function showAnalysisResults(analysis) {
            const resultsDiv = document.getElementById('analysis-results');
            const contentDiv = document.getElementById('analysis-content');
            
            contentDiv.innerHTML = `
                <div class="analysis-item">
                    <h4>📈 Marktstruktur</h4>
                    <p><strong>Trend:</strong> ${analysis.market_structure.trend}</p>
                    <p><strong>Stärke:</strong> ${(analysis.market_structure.strength * 100).toFixed(1)}%</p>
                    <p><strong>Konfidenz:</strong> ${(analysis.market_structure.confidence * 100).toFixed(1)}%</p>
                </div>
                <div class="analysis-item">
                    <h4>💰 Premium/Discount</h4>
                    <p><strong>Zone:</strong> ${analysis.premium_discount.zone}</p>
                    <p><strong>Risiko:</strong> ${analysis.premium_discount.risk_level}</p>
                </div>
                <div class="analysis-item">
                    <h4>⚡ Fair Value Gaps</h4>
                    <p><strong>Erkannt:</strong> ${analysis.fair_value_gaps.length}</p>
                    <p><strong>Ungefüllt:</strong> ${analysis.fair_value_gaps.filter(gap => !gap.filled).length}</p>
                </div>
                <div class="analysis-item">
                    <h4>🌊 Liquiditätspools</h4>
                    <p><strong>Erkannt:</strong> ${analysis.liquidity_pools.length}</p>
                    <p><strong>Kürzlich abgeholt:</strong> ${analysis.liquidity_pools.filter(pool => pool.swept).length}</p>
                </div>
                <div class="analysis-item">
                    <h4>📊 Gesamt-Konfidenz</h4>
                    <p><strong>Score:</strong> ${(analysis.confidence_score * 100).toFixed(1)}%</p>
                    <p><strong>Zeitstempel:</strong> ${new Date(analysis.analysis_timestamp).toLocaleTimeString()}</p>
                </div>
            `;
            
            resultsDiv.style.display = 'block';
        }
        
        function showTradingSignal(signal) {
            const signalDiv = document.getElementById('signal-card');
            const signalType = document.getElementById('signal-type');
            const confidenceBadge = document.getElementById('confidence-badge');
            const signalDetails = document.getElementById('signal-details');
            const signalReasoning = document.getElementById('signal-reasoning');
            const signalWaiting = document.getElementById('signal-waiting');
            
            // Hide waiting message
            signalWaiting.style.display = 'none';
            
            // Set signal type and styling
            signalType.textContent = signal.signal_type;
            signalDiv.className = 'signal-card ' + (signal.signal_type === 'BUY' ? 'buy' : '');
            
            confidenceBadge.textContent = `${(signal.confidence * 100).toFixed(1)}% Konfidenz`;
            
            signalDetails.innerHTML = `
                <div class="signal-detail">
                    <strong>$${signal.entry_price.toFixed(2)}</strong>
                    <div>Entry</div>
                </div>
                <div class="signal-detail">
                    <strong>$${signal.stop_loss.toFixed(2)}</strong>
                    <div>Stop Loss</div>
                </div>
                <div class="signal-detail">
                    <strong>$${signal.take_profit.toFixed(2)}</strong>
                    <div>Take Profit</div>
                </div>
                <div class="signal-detail">
                    <strong>${signal.risk_reward_ratio}:1</strong>
                    <div>Risk:Reward</div>
                </div>
            `;
            
            signalReasoning.innerHTML = `
                <p><strong>Begründung:</strong> ${signal.reasoning}</p>
                ${signal.expected_duration ? `<p><strong>Erwartete Dauer:</strong> ${signal.expected_duration}</p>` : ''}
            `;
            
            signalDiv.style.display = 'block';
            
            // Calculate and display lot size
            const accountBalance = parseFloat(document.getElementById('account-balance-input').value);
            const riskPercentage = parseFloat(document.getElementById('risk-percentage').value);
            const lotSize = calculateLotSize(accountBalance, riskPercentage, signal.entry_price, signal.stop_loss);
            
            signalDetails.innerHTML += `
                <div class="signal-detail">
                    <strong>${lotSize}</strong>
                    <div>Lot Size</div>
                </div>
            `;
        }
        
        function calculateLotSize(accountBalance, riskPercentage, entryPrice, stopLoss) {
            const riskAmount = accountBalance * (riskPercentage / 100);
            const riskInPrice = Math.abs(entryPrice - stopLoss);
            const pipValue = 0.01; // For Gold
            const pipSize = 0.01;
            const riskInPips = riskInPrice / pipSize;
            
            if (riskInPips > 0) {
                let lotSize = riskAmount / (riskInPips * pipValue * 100);
                const maxLotSize = accountBalance / 1000;
                lotSize = Math.min(lotSize, maxLotSize);
                return Math.max(lotSize, 0.01).toFixed(2);
            }
            
            return '0.01';
        }
        
        function drawChart() {
            if (!chartContext || currentData.length === 0) return;
            
            const canvas = chartContext.canvas;
            const width = canvas.width;
            const height = canvas.height;
            
            chartContext.clearRect(0, 0, width, height);
            
            const prices = currentData.map(d => d.close);
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const priceRange = maxPrice - minPrice;
            
            // Draw grid
            chartContext.strokeStyle = 'rgba(255,255,255,0.1)';
            chartContext.lineWidth = 1;
            
            for (let i = 0; i <= 10; i++) {
                const y = (height / 10) * i;
                chartContext.beginPath();
                chartContext.moveTo(0, y);
                chartContext.lineTo(width, y);
                chartContext.stroke();
            }
            
            // Draw price line
            chartContext.strokeStyle = 'var(--primary-color)';
            chartContext.lineWidth = 3;
            chartContext.beginPath();
            
            currentData.forEach((point, index) => {
                const x = (index / (currentData.length - 1)) * (width - 40) + 20;
                const y = height - 20 - ((point.close - minPrice) / priceRange) * (height - 40);
                
                if (index === 0) {
                    chartContext.moveTo(x, y);
                } else {
                    chartContext.lineTo(x, y);
                }
            });
            
            chartContext.stroke();
            
            // Draw current price point
            if (currentData.length > 0) {
                const lastPoint = currentData[currentData.length - 1];
                const x = width - 20;
                const y = height - 20 - ((lastPoint.close - minPrice) / priceRange) * (height - 40);
                
                chartContext.fillStyle = 'var(--primary-color)';
                chartContext.beginPath();
                chartContext.arc(x, y, 6, 0, 2 * Math.PI);
                chartContext.fill();
                
                // Price label
                chartContext.fillStyle = 'white';
                chartContext.font = '12px Arial';
                chartContext.fillText(`$${lastPoint.close.toFixed(2)}`, x - 30, y - 10);
            }
        }
        
        function setStatus(message, type = 'info') {
            const statusText = document.getElementById('status-text');
            const statusBar = document.getElementById('status-bar');
            const statusIcon = statusBar.querySelector('.status-icon');
            
            statusText.textContent = message;
            
            // Remove existing classes
            statusBar.classList.remove('loading');
            
            // Set icon color based on type
            switch (type) {
                case 'success':
                    statusIcon.style.background = 'var(--success-color)';
                    break;
                case 'error':
                    statusIcon.style.background = 'var(--danger-color)';
                    break;
                case 'loading':
                    statusIcon.style.background = 'var(--info-color)';
                    statusBar.classList.add('loading');
                    break;
                default:
                    statusIcon.style.background = 'var(--primary-color)';
            }
        }
        
        function toggleConfig() {
            const configPanel = document.getElementById('config-panel');
            const isVisible = configPanel.style.display === 'block';
            configPanel.style.display = isVisible ? 'none' : 'block';
        }
        
        function saveConfig() {
            const accountBalance = document.getElementById('account-balance-input').value;
            const riskPercentage = document.getElementById('risk-percentage').value;
            const autoTrading = document.getElementById('auto-trading').value;
            const riskReward = document.getElementById('risk-reward').value;
            
            // Update display
            document.getElementById('account-balance').textContent = '$' + parseFloat(accountBalance).toFixed(2);
            
            setStatus('✅ Konfiguration gespeichert', 'success');
            
            // Hide config panel
            setTimeout(() => {
                toggleConfig();
            }, 1000);
        }
        
        function executeTrade() {
            setStatus('🚀 Trade wird ausgeführt...', 'loading');
            
            // Simulate trade execution
            setTimeout(() => {
                const activeTradesElement = document.getElementById('active-trades');
                const currentTrades = parseInt(activeTradesElement.textContent);
                activeTradesElement.textContent = currentTrades + 1;
                
                setStatus('✅ Trade erfolgreich ausgeführt', 'success');
                
                // Hide signal card
                document.getElementById('signal-card').style.display = 'none';
                document.getElementById('signal-waiting').style.display = 'block';
            }, 2000);
        }
        
        function toggleTheme() {
            // Simple theme toggle (placeholder)
            const body = document.body;
            body.style.filter = body.style.filter === 'invert(1)' ? '' : 'invert(1)';
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        runAnalysis();
                        break;
                    case '2':
                        e.preventDefault();
                        runAutoAnalysis();
                        break;
                    case '3':
                        e.preventDefault();
                        updateData();
                        break;
                    case '4':
                        e.preventDefault();
                        toggleConfig();
                        break;
                }
            }
        });
        
        // Service Worker registration for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed');
                    });
            });
        }
    </script>
</body>
</html>

