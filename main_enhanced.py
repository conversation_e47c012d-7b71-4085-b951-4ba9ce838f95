import os
import time
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS
import json
import random
import math
from datetime import datetime, timedelta

# Import unserer verbesserten Module
try:
    from ict_analyzer_enhanced import EnhancedICTAnalyzer
    from market_data_enhanced import enhanced_market_data_provider
    from security_manager import security_manager, SecurityException
    from performance_monitor import performance_monitor, track_performance
except ImportError as e:
    print(f"Import-Warnung: {e}")
    # Fallback zu einfachen Implementierungen
    EnhancedICTAnalyzer = None
    enhanced_market_data_provider = None
    security_manager = None
    performance_monitor = None

app = Flask(__name__)
CORS(app)

# Initialisiere Komponenten
if EnhancedICTAnalyzer:
    ict_analyzer = EnhancedICTAnalyzer()
else:
    ict_analyzer = None

if performance_monitor:
    performance_monitor.start_monitoring()

# Globale Konfiguration
app_config = {
    'account_balance': 100.0,
    'risk_percentage': 10.0,
    'auto_trading_enabled': True,
    'risk_reward_ratio': 1.5,
    'max_concurrent_trades': 3,
    'trading_session_active': True
}

# Trading-Status
trading_status = {
    'active_trades': 0,
    'total_pnl': 0.0,
    'last_signal': None,
    'last_analysis': None,
    'session_start': datetime.utcnow()
}

@app.route('/')
def index():
    """Hauptseite mit verbessertem Frontend"""
    try:
        return send_from_directory('static', 'enhanced_index.html')
    except:
        # Fallback zur einfachen Version
        return render_template_string(get_simple_template())

@app.route('/static/<path:filename>')
def static_files(filename):
    """Statische Dateien servieren"""
    return send_from_directory('static', filename)

@app.route('/api/market-data')
def api_market_data():
    """Verbesserte Marktdaten-API"""
    try:
        # Sicherheitsvalidierung
        if security_manager and not security_manager.check_rate_limit('127.0.0.1'):
            raise SecurityException("Rate limit exceeded")
        
        # Hole Marktdaten
        if enhanced_market_data_provider:
            data = enhanced_market_data_provider.get_gold_data(limit=100)
        else:
            data = get_simple_gold_data()
        
        # Füge Metadaten hinzu
        response_data = {
            'data': data,
            'timestamp': datetime.utcnow().isoformat(),
            'source': 'enhanced' if enhanced_market_data_provider else 'simple',
            'market_status': get_market_status()
        }
        
        return jsonify(response_data)
        
    except SecurityException as e:
        return jsonify({'error': 'Security violation', 'message': str(e)}), 429
    except Exception as e:
        if performance_monitor:
            performance_monitor.track_error('market_data_error', str(e))
        return jsonify({'error': 'Data unavailable', 'message': str(e)}), 500

@app.route('/api/ict-analysis')
def api_ict_analysis():
    """Erweiterte ICT-Analyse-API"""
    try:
        # Sicherheitsvalidierung
        if security_manager and not security_manager.check_rate_limit('127.0.0.1'):
            raise SecurityException("Rate limit exceeded")
        
        # Hole aktuelle Marktdaten
        if enhanced_market_data_provider:
            market_data = enhanced_market_data_provider.get_gold_data(limit=100)
        else:
            market_data = get_simple_gold_data()
        
        # Führe ICT-Analyse durch
        if ict_analyzer:
            analysis = ict_analyzer.analyze_market_data(market_data)
        else:
            analysis = get_simple_analysis(market_data)
        
        # Speichere letzte Analyse
        trading_status['last_analysis'] = analysis
        
        # Berechne Lotgröße für potenzielle Signale
        if analysis.get('trading_signal'):
            signal = analysis['trading_signal']
            if ict_analyzer:
                lot_size = ict_analyzer.calculate_lot_size(
                    app_config['account_balance'],
                    app_config['risk_percentage'],
                    signal['entry_price'],
                    signal['stop_loss']
                )
                signal['lot_size'] = lot_size
            
            trading_status['last_signal'] = signal
        
        return jsonify(analysis)
        
    except SecurityException as e:
        return jsonify({'error': 'Security violation', 'message': str(e)}), 429
    except Exception as e:
        if performance_monitor:
            performance_monitor.track_error('ict_analysis_error', str(e))
        return jsonify({'error': 'Analysis failed', 'message': str(e)}), 500

@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """Konfiguration verwalten"""
    try:
        if request.method == 'GET':
            return jsonify(app_config)
        
        elif request.method == 'POST':
            # Validiere und aktualisiere Konfiguration
            data = request.get_json()
            
            if security_manager:
                data = security_manager.sanitize_input(data)
                valid, message = security_manager.validate_trading_request(data) if 'signal_type' in data else (True, "OK")
                if not valid:
                    return jsonify({'error': 'Invalid configuration', 'message': message}), 400
            
            # Aktualisiere Konfiguration
            for key, value in data.items():
                if key in app_config:
                    app_config[key] = value
            
            return jsonify({'status': 'success', 'config': app_config})
            
    except Exception as e:
        return jsonify({'error': 'Configuration error', 'message': str(e)}), 500

@app.route('/api/trading-status')
def api_trading_status():
    """Trading-Status abrufen"""
    try:
        status = {
            **trading_status,
            'config': app_config,
            'uptime': str(datetime.utcnow() - trading_status['session_start']),
            'performance': get_performance_summary() if performance_monitor else None
        }
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': 'Status unavailable', 'message': str(e)}), 500

@app.route('/api/execute-trade', methods=['POST'])
def api_execute_trade():
    """Trade ausführen (Demo)"""
    try:
        data = request.get_json()
        
        # Sicherheitsvalidierung
        if security_manager:
            data = security_manager.sanitize_input(data)
            valid, message = security_manager.validate_trading_request(data)
            if not valid:
                return jsonify({'error': 'Invalid trade request', 'message': message}), 400
        
        # Simuliere Trade-Ausführung
        trade_result = {
            'trade_id': f"TRADE_{int(time.time())}",
            'status': 'executed',
            'signal_type': data.get('signal_type', 'BUY'),
            'entry_price': data.get('entry_price', 2000.0),
            'lot_size': data.get('lot_size', 0.01),
            'timestamp': datetime.utcnow().isoformat(),
            'estimated_profit': random.uniform(-50, 100)  # Demo P&L
        }
        
        # Aktualisiere Trading-Status
        trading_status['active_trades'] += 1
        trading_status['total_pnl'] += trade_result['estimated_profit']
        
        return jsonify(trade_result)
        
    except SecurityException as e:
        return jsonify({'error': 'Security violation', 'message': str(e)}), 429
    except Exception as e:
        return jsonify({'error': 'Trade execution failed', 'message': str(e)}), 500

@app.route('/api/performance')
def api_performance():
    """Performance-Metriken abrufen"""
    try:
        if performance_monitor:
            summary = performance_monitor.get_performance_summary()
            health = performance_monitor.get_health_status()
            return jsonify({
                'performance': summary,
                'health': health,
                'monitoring_active': performance_monitor.monitoring_active
            })
        else:
            return jsonify({'error': 'Performance monitoring not available'}), 503
    except Exception as e:
        return jsonify({'error': 'Performance data unavailable', 'message': str(e)}), 500

@app.route('/api/security-status')
def api_security_status():
    """Sicherheitsstatus abrufen"""
    try:
        if security_manager:
            return jsonify({
                'rate_limits_active': len(security_manager.rate_limits),
                'blocked_ips': len(security_manager.blocked_ips),
                'session_tokens': len(security_manager.session_tokens),
                'security_headers': security_manager.get_security_headers()
            })
        else:
            return jsonify({'error': 'Security manager not available'}), 503
    except Exception as e:
        return jsonify({'error': 'Security status unavailable', 'message': str(e)}), 500

@app.route('/offline.html')
def offline_page():
    """Offline-Seite für PWA"""
    return '''
    <!DOCTYPE html>
    <html lang="de">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Offline - Gold Trading App</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #1e3c72, #2a5298);
                color: white;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                margin: 0;
                text-align: center;
            }
            .offline-container {
                max-width: 500px;
                padding: 40px;
                background: rgba(255,255,255,0.1);
                border-radius: 20px;
                backdrop-filter: blur(10px);
            }
            .offline-icon { font-size: 4rem; margin-bottom: 20px; }
            h1 { color: #ffd700; margin-bottom: 20px; }
            .retry-btn {
                background: #ffd700;
                color: #000;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 1.1rem;
                cursor: pointer;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="offline-container">
            <div class="offline-icon">📡</div>
            <h1>Offline-Modus</h1>
            <p>Die Gold Trading App ist derzeit offline.</p>
            <button class="retry-btn" onclick="window.location.reload()">
                🔄 Verbindung erneut versuchen
            </button>
        </div>
    </body>
    </html>
    '''

# Hilfsfunktionen
def get_simple_gold_data():
    """Einfache Marktdaten-Generierung als Fallback"""
    base_price = 2000.0
    current_time = datetime.utcnow()
    data = []
    
    for i in range(100):
        timestamp = current_time - timedelta(minutes=100-i)
        price = base_price + random.gauss(0, 10) + math.sin(i/10) * 5
        
        data.append({
            'timestamp': timestamp.isoformat(),
            'open': price,
            'high': price + random.uniform(0, 5),
            'low': price - random.uniform(0, 5),
            'close': price + random.gauss(0, 2),
            'volume': random.randint(100, 1000)
        })
    
    return data

def get_simple_analysis(data):
    """Einfache ICT-Analyse als Fallback"""
    if not data:
        return {
            'market_structure': {'trend': 'sideways', 'strength': 0.5, 'current_price': 2000.0},
            'premium_discount': {'zone': 'neutral', 'risk_level': 'medium'},
            'fair_value_gaps': [],
            'liquidity_pools': [],
            'trading_signal': None
        }
    
    current_price = data[-1]['close']
    prices = [item['close'] for item in data[-20:]]
    trend = 'bullish' if prices[-1] > prices[0] else 'bearish'
    
    # Generiere gelegentlich Signale
    signal = None
    if random.random() > 0.7:  # 30% Chance für Signal
        signal = {
            'signal_type': 'BUY' if trend == 'bullish' else 'SELL',
            'confidence': 0.6,
            'entry_price': current_price,
            'stop_loss': current_price - 10 if trend == 'bullish' else current_price + 10,
            'take_profit': current_price + 15 if trend == 'bullish' else current_price - 15,
            'reasoning': f'{trend.title()} trend erkannt'
        }
    
    return {
        'market_structure': {
            'trend': trend,
            'strength': 0.6,
            'current_price': current_price
        },
        'premium_discount': {
            'zone': 'discount' if current_price < 2010 else 'premium',
            'risk_level': 'low'
        },
        'fair_value_gaps': [
            {'start_price': current_price - 5, 'end_price': current_price + 5, 'gap_type': 'bullish', 'filled': False}
        ],
        'liquidity_pools': [
            {'price': current_price + 10, 'pool_type': 'buy_stops', 'strength': 0.8}
        ],
        'trading_signal': signal
    }

def get_market_status():
    """Marktstatusinformationen"""
    current_time = datetime.utcnow()
    hour = current_time.hour
    
    if 8 <= hour <= 17:
        session = "London/NY"
        activity = "Hoch"
    elif 22 <= hour <= 23 or 0 <= hour <= 2:
        session = "Asien"
        activity = "Mittel"
    else:
        session = "Ruhig"
        activity = "Niedrig"
    
    return {
        'session': session,
        'activity_level': activity,
        'market_open': True,
        'server_time': current_time.isoformat()
    }

def get_performance_summary():
    """Performance-Zusammenfassung"""
    if performance_monitor:
        return performance_monitor.get_performance_summary()
    return None

def get_simple_template():
    """Einfaches HTML-Template als Fallback"""
    return '''
    <!DOCTYPE html>
    <html lang="de">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Gold Trading App</title>
        <style>
            body { font-family: Arial, sans-serif; background: #1e3c72; color: white; padding: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
            .btn-primary { background: #ffd700; color: #000; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🥇 Gold Trading App</h1>
            <p>Einfache Version - Erweiterte Features werden geladen...</p>
            <button class="btn btn-primary" onclick="location.reload()">🔄 Neu laden</button>
        </div>
    </body>
    </html>
    '''

# Error Handler
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

@app.before_request
def before_request():
    """Sicherheits-Header für alle Requests"""
    if security_manager:
        # Logge Request
        security_manager.log_security_event('request', {
            'endpoint': request.endpoint,
            'method': request.method,
            'client_ip': request.remote_addr
        })

@app.after_request
def after_request(response):
    """Füge Sicherheits-Header hinzu"""
    if security_manager:
        headers = security_manager.get_security_headers()
        for key, value in headers.items():
            response.headers[key] = value
    
    return response

if __name__ == '__main__':
    print("🚀 Starte Enhanced Gold Trading App...")
    print(f"📊 ICT Analyzer: {'✅ Aktiviert' if ict_analyzer else '❌ Fallback'}")
    print(f"📡 Market Data: {'✅ Enhanced' if enhanced_market_data_provider else '❌ Simple'}")
    print(f"🔒 Security: {'✅ Aktiviert' if security_manager else '❌ Deaktiviert'}")
    print(f"📈 Performance: {'✅ Monitoring' if performance_monitor else '❌ Deaktiviert'}")
    
    port = int(os.environ.get('PORT', 5001))
    app.run(host='0.0.0.0', port=port, debug=False)

