import random
import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

@dataclass
class EnhancedPriceLevel:
    price: float
    timestamp: datetime
    level_type: str  # 'support', 'resistance', 'high', 'low'
    strength: float  # 0.0 to 1.0
    touches: int = 1  # Anzahl der Berührungen
    volume: float = 0.0  # Volumen an diesem Level

@dataclass
class EnhancedFairValueGap:
    start_price: float
    end_price: float
    timestamp: datetime
    gap_type: str  # 'bullish', 'bearish'
    filled: bool = False
    fill_percentage: float = 0.0  # Wie viel % des Gaps bereits gefüllt
    strength: float = 0.0  # Stärke des Gaps basierend auf Volumen/Momentum

@dataclass
class EnhancedLiquidityPool:
    price: float
    timestamp: datetime
    pool_type: str  # 'buy_stops', 'sell_stops'
    strength: float  # 0.0 to 1.0
    estimated_volume: float = 0.0  # Geschätztes Volumen
    swept: bool = False  # Wurde bereits abgeholt

@dataclass
class MarketStructurePoint:
    price: float
    timestamp: datetime
    point_type: str  # 'HH', 'HL', 'LH', 'LL'
    confirmed: bool = False

class EnhancedICTAnalyzer:
    def __init__(self):
        self.lookback_periods = 100  # Mehr Daten für bessere Analyse
        self.gap_threshold = 0.0003  # Reduziert für Gold (0.03%)
        self.liquidity_threshold = 0.0008  # Angepasst für Gold
        self.structure_confirmation_periods = 3  # Bestätigung für Struktur-Punkte
        
    def analyze_market_data(self, ohlc_data: List[Dict]) -> Dict:
        """
        Erweiterte ICT-Analyse mit verbesserter Genauigkeit
        """
        if len(ohlc_data) < self.lookback_periods:
            return self._empty_analysis()
            
        # Konvertiere und sortiere Daten
        data = self._prepare_data(ohlc_data)
        
        # Erweiterte Marktstruktur-Analyse
        market_structure = self._analyze_enhanced_market_structure(data)
        
        # Verbesserte Fair Value Gap Erkennung
        fair_value_gaps = self._identify_enhanced_fair_value_gaps(data)
        
        # Erweiterte Liquiditätspool-Erkennung
        liquidity_pools = self._identify_enhanced_liquidity_pools(data)
        
        # Verbesserte Premium/Discount Analyse
        premium_discount = self._analyze_enhanced_premium_discount(data, market_structure)
        
        # Erweiterte Signal-Generierung
        signal = self._generate_enhanced_trading_signal(
            data, market_structure, fair_value_gaps, liquidity_pools, premium_discount
        )
        
        # Zusätzliche Analysen
        order_blocks = self._identify_order_blocks(data)
        breaker_blocks = self._identify_breaker_blocks(data, market_structure)
        
        return {
            'market_structure': market_structure,
            'fair_value_gaps': fair_value_gaps,
            'liquidity_pools': liquidity_pools,
            'premium_discount': premium_discount,
            'order_blocks': order_blocks,
            'breaker_blocks': breaker_blocks,
            'trading_signal': signal,
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'confidence_score': self._calculate_overall_confidence(signal, market_structure, fair_value_gaps)
        }
    
    def _prepare_data(self, ohlc_data: List[Dict]) -> List[Dict]:
        """Bereitet Daten für Analyse vor"""
        data = []
        for item in ohlc_data:
            data.append({
                'timestamp': datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00')) if isinstance(item['timestamp'], str) else item['timestamp'],
                'open': float(item['open']),
                'high': float(item['high']),
                'low': float(item['low']),
                'close': float(item['close']),
                'volume': float(item.get('volume', 0))
            })
        
        data.sort(key=lambda x: x['timestamp'])
        return data
    
    def _analyze_enhanced_market_structure(self, data: List[Dict]) -> Dict:
        """
        Erweiterte Marktstruktur-Analyse mit Bestätigung
        """
        # Finde Swing-Punkte mit verschiedenen Timeframes
        swing_highs_5 = self._find_swing_highs(data, window=5)
        swing_lows_5 = self._find_swing_lows(data, window=5)
        swing_highs_10 = self._find_swing_highs(data, window=10)
        swing_lows_10 = self._find_swing_lows(data, window=10)
        
        # Kombiniere für robustere Analyse
        confirmed_highs = self._confirm_swing_points(swing_highs_5, swing_highs_10)
        confirmed_lows = self._confirm_swing_points(swing_lows_5, swing_lows_10)
        
        # Analysiere Struktur-Sequenzen
        structure_points = self._identify_structure_sequence(confirmed_highs, confirmed_lows)
        
        # Bestimme Trend mit Konfidenz
        trend_analysis = self._determine_trend_with_confidence(structure_points)
        
        # Identifiziere Struktur-Brüche
        structure_breaks = self._identify_structure_breaks(structure_points, data)
        
        return {
            'trend': trend_analysis['trend'],
            'strength': trend_analysis['strength'],
            'confidence': trend_analysis['confidence'],
            'current_price': float(data[-1]['close']),
            'structure_points': structure_points,
            'structure_breaks': structure_breaks,
            'key_levels': self._identify_enhanced_key_levels(confirmed_highs, confirmed_lows, data),
            'trend_change_probability': self._calculate_trend_change_probability(structure_points, data)
        }
    
    def _identify_enhanced_fair_value_gaps(self, data: List[Dict]) -> List[Dict]:
        """
        Verbesserte Fair Value Gap Erkennung mit Stärke-Bewertung
        """
        gaps = []
        
        for i in range(2, len(data)):
            # Bullish FVG: Low[i] > High[i-2]
            if data[i]['low'] > data[i-2]['high']:
                gap_size = data[i]['low'] - data[i-2]['high']
                gap_percentage = gap_size / data[i-1]['close']
                
                if gap_percentage > self.gap_threshold:
                    # Berechne Gap-Stärke basierend auf Volumen und Momentum
                    strength = self._calculate_gap_strength(data, i, 'bullish')
                    
                    gap = {
                        'start_price': float(data[i-2]['high']),
                        'end_price': float(data[i]['low']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'gap_type': 'bullish',
                        'size': float(gap_size),
                        'percentage': float(gap_percentage * 100),
                        'strength': strength,
                        'filled': False,
                        'fill_percentage': 0.0
                    }
                    gaps.append(gap)
            
            # Bearish FVG: High[i] < Low[i-2]
            elif data[i]['high'] < data[i-2]['low']:
                gap_size = data[i-2]['low'] - data[i]['high']
                gap_percentage = gap_size / data[i-1]['close']
                
                if gap_percentage > self.gap_threshold:
                    strength = self._calculate_gap_strength(data, i, 'bearish')
                    
                    gap = {
                        'start_price': float(data[i]['high']),
                        'end_price': float(data[i-2]['low']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'gap_type': 'bearish',
                        'size': float(gap_size),
                        'percentage': float(gap_percentage * 100),
                        'strength': strength,
                        'filled': False,
                        'fill_percentage': 0.0
                    }
                    gaps.append(gap)
        
        # Prüfe Gap-Füllungen und berechne Füllungsgrad
        current_price = data[-1]['close']
        for gap in gaps:
            if gap['gap_type'] == 'bullish':
                if current_price <= gap['start_price']:
                    gap['filled'] = True
                    gap['fill_percentage'] = 100.0
                elif current_price < gap['end_price']:
                    gap['fill_percentage'] = ((gap['end_price'] - current_price) / 
                                            (gap['end_price'] - gap['start_price'])) * 100
            else:  # bearish
                if current_price >= gap['end_price']:
                    gap['filled'] = True
                    gap['fill_percentage'] = 100.0
                elif current_price > gap['start_price']:
                    gap['fill_percentage'] = ((current_price - gap['start_price']) / 
                                            (gap['end_price'] - gap['start_price'])) * 100
                    
        return gaps
    
    def _identify_enhanced_liquidity_pools(self, data: List[Dict]) -> List[Dict]:
        """
        Erweiterte Liquiditätspool-Erkennung
        """
        pools = []
        
        # Identifiziere Bereiche mit hoher Volatilität und Volumen
        for i in range(1, len(data)):
            price_change = abs(data[i]['close'] - data[i-1]['close'])
            avg_price = (data[i]['close'] + data[i-1]['close']) / 2
            volume_ratio = data[i]['volume'] / max(data[i-1]['volume'], 1)
            
            # Erweiterte Kriterien für Liquiditäts-Sweeps
            if (price_change / avg_price > self.liquidity_threshold and 
                volume_ratio > 1.2):  # Erhöhtes Volumen
                
                # Bestimme Pool-Typ und Stärke
                if data[i]['close'] > data[i-1]['close']:
                    # Aufwärtsbewegung - Buy-Stops getroffen
                    strength = min((price_change / avg_price) * volume_ratio * 5, 1.0)
                    pools.append({
                        'price': float(data[i]['high']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'pool_type': 'buy_stops',
                        'strength': strength,
                        'volume': float(data[i]['volume']),
                        'price_change': float(price_change),
                        'swept': True
                    })
                else:
                    # Abwärtsbewegung - Sell-Stops getroffen
                    strength = min((price_change / avg_price) * volume_ratio * 5, 1.0)
                    pools.append({
                        'price': float(data[i]['low']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'pool_type': 'sell_stops',
                        'strength': strength,
                        'volume': float(data[i]['volume']),
                        'price_change': float(price_change),
                        'swept': True
                    })
        
        # Identifiziere potenzielle zukünftige Liquiditätspools
        swing_highs = self._find_swing_highs(data, window=5)
        swing_lows = self._find_swing_lows(data, window=5)
        
        for high in swing_highs[-3:]:  # Letzte 3 Highs
            pools.append({
                'price': high['price'],
                'timestamp': high['timestamp'],
                'pool_type': 'buy_stops',
                'strength': 0.6,  # Potenzielle Pools haben niedrigere Stärke
                'volume': 0,
                'price_change': 0,
                'swept': False
            })
            
        for low in swing_lows[-3:]:  # Letzte 3 Lows
            pools.append({
                'price': low['price'],
                'timestamp': low['timestamp'],
                'pool_type': 'sell_stops',
                'strength': 0.6,
                'volume': 0,
                'price_change': 0,
                'swept': False
            })
        
        return pools
    
    def _identify_order_blocks(self, data: List[Dict]) -> List[Dict]:
        """
        Identifiziert Order Blocks (Institutionelle Aufträge)
        """
        order_blocks = []
        
        for i in range(10, len(data) - 5):
            # Suche nach starken Bewegungen nach Konsolidierung
            consolidation_range = max([d['high'] for d in data[i-10:i]]) - min([d['low'] for d in data[i-10:i]])
            avg_price = sum([d['close'] for d in data[i-10:i]]) / 10
            
            # Starke Bewegung nach Konsolidierung
            movement = abs(data[i+5]['close'] - data[i]['close'])
            
            if movement > consolidation_range * 1.5:  # Bewegung ist 1.5x größer als Konsolidierung
                order_block = {
                    'high': float(max([d['high'] for d in data[i-2:i+2]])),
                    'low': float(min([d['low'] for d in data[i-2:i+2]])),
                    'timestamp': data[i]['timestamp'].isoformat(),
                    'direction': 'bullish' if data[i+5]['close'] > data[i]['close'] else 'bearish',
                    'strength': min(movement / (avg_price * 0.01), 1.0),  # Normalisierte Stärke
                    'tested': False
                }
                order_blocks.append(order_block)
        
        return order_blocks
    
    def _identify_breaker_blocks(self, data: List[Dict], market_structure: Dict) -> List[Dict]:
        """
        Identifiziert Breaker Blocks (gebrochene Struktur-Levels)
        """
        breaker_blocks = []
        
        structure_breaks = market_structure.get('structure_breaks', [])
        
        for break_point in structure_breaks:
            # Finde den Bereich um den Struktur-Bruch
            break_index = None
            for i, d in enumerate(data):
                if d['timestamp'].isoformat() == break_point['timestamp']:
                    break_index = i
                    break
            
            if break_index and break_index > 5:
                # Definiere Breaker Block Bereich
                if break_point['break_type'] == 'bullish':
                    # Letzter bearish Order Block vor dem Bruch
                    high = float(max([d['high'] for d in data[break_index-5:break_index]]))
                    low = float(min([d['low'] for d in data[break_index-5:break_index]]))
                else:
                    # Letzter bullish Order Block vor dem Bruch
                    high = float(max([d['high'] for d in data[break_index-5:break_index]]))
                    low = float(min([d['low'] for d in data[break_index-5:break_index]]))
                
                breaker_blocks.append({
                    'high': high,
                    'low': low,
                    'timestamp': break_point['timestamp'],
                    'break_type': break_point['break_type'],
                    'strength': 0.8,
                    'retested': False
                })
        
        return breaker_blocks
    
    def _generate_enhanced_trading_signal(self, data: List[Dict], market_structure: Dict, 
                                        fair_value_gaps: List[Dict], liquidity_pools: List[Dict],
                                        premium_discount: Dict) -> Optional[Dict]:
        """
        Erweiterte Signal-Generierung mit mehreren Bestätigungen
        """
        current_price = data[-1]['close']
        
        # Multi-Faktor Signal-Bewertung
        signal_factors = {
            'market_structure': 0.0,
            'fair_value_gaps': 0.0,
            'liquidity_pools': 0.0,
            'premium_discount': 0.0,
            'momentum': 0.0,
            'volume': 0.0
        }
        
        reasoning_parts = []
        
        # 1. Market Structure Bewertung (30% Gewichtung)
        if market_structure['trend'] == 'bullish' and market_structure['confidence'] > 0.6:
            signal_factors['market_structure'] = 0.3 * market_structure['confidence']
            reasoning_parts.append(f"Starke bullische Struktur ({market_structure['confidence']*100:.1f}%)")
        elif market_structure['trend'] == 'bearish' and market_structure['confidence'] > 0.6:
            signal_factors['market_structure'] = -0.3 * market_structure['confidence']
            reasoning_parts.append(f"Starke bärische Struktur ({market_structure['confidence']*100:.1f}%)")
        
        # 2. Fair Value Gaps Bewertung (25% Gewichtung)
        unfilled_gaps = [gap for gap in fair_value_gaps if not gap['filled'] and gap['strength'] > 0.5]
        if unfilled_gaps:
            latest_gap = max(unfilled_gaps, key=lambda x: x['strength'])
            if latest_gap['gap_type'] == 'bullish' and current_price >= latest_gap['start_price'] * 0.999:
                signal_factors['fair_value_gaps'] = 0.25 * latest_gap['strength']
                reasoning_parts.append(f"Starkes bullish FVG bei {latest_gap['start_price']:.2f}")
            elif latest_gap['gap_type'] == 'bearish' and current_price <= latest_gap['end_price'] * 1.001:
                signal_factors['fair_value_gaps'] = -0.25 * latest_gap['strength']
                reasoning_parts.append(f"Starkes bearish FVG bei {latest_gap['end_price']:.2f}")
        
        # 3. Premium/Discount Bewertung (20% Gewichtung)
        if premium_discount['zone'] == 'discount' and premium_discount['risk_level'] == 'low':
            signal_factors['premium_discount'] = 0.2
            reasoning_parts.append("Preis in optimaler Discount-Zone")
        elif premium_discount['zone'] == 'premium' and premium_discount['risk_level'] == 'high':
            signal_factors['premium_discount'] = -0.2
            reasoning_parts.append("Preis in risikoreicher Premium-Zone")
        
        # 4. Liquiditätspools Bewertung (15% Gewichtung)
        recent_pools = [pool for pool in liquidity_pools if pool['swept'] and pool['strength'] > 0.7]
        if recent_pools:
            latest_pool = recent_pools[-1]
            if latest_pool['pool_type'] == 'sell_stops':
                signal_factors['liquidity_pools'] = 0.15
                reasoning_parts.append("Sell-Stops kürzlich abgeholt")
            else:
                signal_factors['liquidity_pools'] = -0.15
                reasoning_parts.append("Buy-Stops kürzlich abgeholt")
        
        # 5. Momentum Bewertung (10% Gewichtung)
        momentum = self._calculate_momentum(data)
        if momentum > 0.6:
            signal_factors['momentum'] = 0.1
            reasoning_parts.append("Starkes bullisches Momentum")
        elif momentum < -0.6:
            signal_factors['momentum'] = -0.1
            reasoning_parts.append("Starkes bärisches Momentum")
        
        # Gesamtsignal berechnen
        total_signal_strength = sum(signal_factors.values())
        
        # Signal generieren wenn Stärke ausreichend (mindestens 0.5)
        if abs(total_signal_strength) >= 0.5:
            signal_type = 'BUY' if total_signal_strength > 0 else 'SELL'
            
            # Optimierte Entry, SL und TP Berechnung
            entry_price = float(current_price)
            
            if signal_type == 'BUY':
                # Für Buy: SL unter nächstem Support, TP basierend auf RR
                stop_loss = self._calculate_optimal_stop_loss(data, market_structure, 'BUY')
                risk_distance = entry_price - stop_loss
                take_profit = entry_price + (risk_distance * 1.5)  # 1:1.5 RR für bessere Signale
                
            else:  # SELL
                # Für Sell: SL über nächster Resistance, TP basierend auf RR
                stop_loss = self._calculate_optimal_stop_loss(data, market_structure, 'SELL')
                risk_distance = stop_loss - entry_price
                take_profit = entry_price - (risk_distance * 1.5)  # 1:1.5 RR
            
            return {
                'signal_type': signal_type,
                'confidence': abs(total_signal_strength),
                'entry_price': entry_price,
                'stop_loss': float(stop_loss),
                'take_profit': float(take_profit),
                'reasoning': '; '.join(reasoning_parts),
                'risk_reward_ratio': 1.5,
                'signal_factors': signal_factors,
                'expected_duration': self._estimate_trade_duration(market_structure, fair_value_gaps)
            }
        
        return None
    
    def _calculate_optimal_stop_loss(self, data: List[Dict], market_structure: Dict, direction: str) -> float:
        """
        Berechnet optimalen Stop-Loss basierend auf Struktur
        """
        current_price = data[-1]['close']
        key_levels = market_structure.get('key_levels', [])
        
        if direction == 'BUY':
            # Finde nächsten Support unter aktuellem Preis
            supports = [level['price'] for level in key_levels 
                       if level['level_type'] == 'support' and level['price'] < current_price]
            if supports:
                return max(supports) - (current_price * 0.0005)  # Kleiner Buffer
            else:
                return current_price * 0.995  # 0.5% Fallback
        else:  # SELL
            # Finde nächste Resistance über aktuellem Preis
            resistances = [level['price'] for level in key_levels 
                          if level['level_type'] == 'resistance' and level['price'] > current_price]
            if resistances:
                return min(resistances) + (current_price * 0.0005)  # Kleiner Buffer
            else:
                return current_price * 1.005  # 0.5% Fallback
    
    def _calculate_momentum(self, data: List[Dict]) -> float:
        """
        Berechnet Momentum basierend auf Preisbewegung und Volumen
        """
        if len(data) < 10:
            return 0.0
        
        # Preismomentum (letzte 10 Perioden)
        price_changes = []
        for i in range(len(data) - 10, len(data)):
            if i > 0:
                change = (data[i]['close'] - data[i-1]['close']) / data[i-1]['close']
                price_changes.append(change)
        
        avg_price_momentum = sum(price_changes) / len(price_changes) if price_changes else 0
        
        # Volumen-gewichtetes Momentum
        recent_volumes = [d['volume'] for d in data[-10:]]
        avg_volume = sum(recent_volumes) / len(recent_volumes) if recent_volumes else 1
        
        volume_factor = data[-1]['volume'] / avg_volume if avg_volume > 0 else 1
        
        # Kombiniertes Momentum
        momentum = avg_price_momentum * volume_factor * 100  # Skalierung
        
        return max(-1.0, min(1.0, momentum))  # Normalisierung auf [-1, 1]
    
    def _calculate_overall_confidence(self, signal: Optional[Dict], market_structure: Dict, 
                                    fair_value_gaps: List[Dict]) -> float:
        """
        Berechnet Gesamt-Konfidenz der Analyse
        """
        confidence_factors = []
        
        # Signal-Konfidenz
        if signal:
            confidence_factors.append(signal['confidence'])
        
        # Struktur-Konfidenz
        confidence_factors.append(market_structure.get('confidence', 0.5))
        
        # Gap-Qualität
        strong_gaps = [gap for gap in fair_value_gaps if gap['strength'] > 0.7]
        gap_confidence = min(len(strong_gaps) / 3, 1.0)  # Max 3 starke Gaps = 100%
        confidence_factors.append(gap_confidence)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
    
    # Hilfsmethoden (vereinfacht für Platz)
    def _find_swing_highs(self, data: List[Dict], window: int = 5) -> List[Dict]:
        """Vereinfachte Swing-High Erkennung"""
        highs = []
        for i in range(window, len(data) - window):
            current_high = data[i]['high']
            is_swing_high = all(data[j]['high'] <= current_high for j in range(i - window, i + window + 1) if j != i)
            
            if is_swing_high:
                highs.append({
                    'price': float(current_high),
                    'timestamp': data[i]['timestamp'].isoformat(),
                    'index': i
                })
        return highs
    
    def _find_swing_lows(self, data: List[Dict], window: int = 5) -> List[Dict]:
        """Vereinfachte Swing-Low Erkennung"""
        lows = []
        for i in range(window, len(data) - window):
            current_low = data[i]['low']
            is_swing_low = all(data[j]['low'] >= current_low for j in range(i - window, i + window + 1) if j != i)
            
            if is_swing_low:
                lows.append({
                    'price': float(current_low),
                    'timestamp': data[i]['timestamp'].isoformat(),
                    'index': i
                })
        return lows
    
    def _empty_analysis(self) -> Dict:
        """Leere Analyse für unzureichende Daten"""
        return {
            'market_structure': {'trend': 'sideways', 'strength': 0.5, 'confidence': 0.3},
            'fair_value_gaps': [],
            'liquidity_pools': [],
            'premium_discount': {'zone': 'neutral', 'risk_level': 'medium'},
            'order_blocks': [],
            'breaker_blocks': [],
            'trading_signal': None,
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'confidence_score': 0.3
        }
    
    # Weitere Hilfsmethoden (Platzhalter)
    def _confirm_swing_points(self, points1, points2): return points1
    def _identify_structure_sequence(self, highs, lows): return []
    def _determine_trend_with_confidence(self, points): return {'trend': 'sideways', 'strength': 0.5, 'confidence': 0.5}
    def _identify_structure_breaks(self, points, data): return []
    def _identify_enhanced_key_levels(self, highs, lows, data): return []
    def _calculate_trend_change_probability(self, points, data): return 0.3
    def _calculate_gap_strength(self, data, index, gap_type): return 0.6
    def _analyze_enhanced_premium_discount(self, data, structure): 
        return {'zone': 'neutral', 'risk_level': 'medium', 'price_position': 0.5}
    def _estimate_trade_duration(self, structure, gaps): return "2-4 Stunden"

    def calculate_lot_size(self, account_balance: float, risk_percentage: float, 
                          entry_price: float, stop_loss: float, 
                          symbol: str = 'XAUUSD') -> float:
        """
        Erweiterte Lotgrößen-Berechnung mit Risikomanagement
        """
        risk_amount = account_balance * (risk_percentage / 100)
        
        # Symbol-spezifische Pip-Werte
        if symbol == 'XAUUSD':
            pip_value = 0.01  # 1 Pip = $0.01 für 0.01 Lot
            pip_size = 0.01   # 1 Pip = 0.01 für Gold
        else:
            pip_value = 0.0001
            pip_size = 0.0001
        
        # Berechne Risiko in Pips
        risk_in_price = abs(entry_price - stop_loss)
        risk_in_pips = risk_in_price / pip_size
        
        # Berechne optimale Lotgröße
        if risk_in_pips > 0:
            lot_size = risk_amount / (risk_in_pips * pip_value * 100)
            
            # Begrenze Lotgröße für Sicherheit
            max_lot_size = account_balance / 1000  # Max 1 Lot pro $1000
            lot_size = min(lot_size, max_lot_size)
            
            return round(max(lot_size, 0.01), 2)  # Minimum 0.01 Lot
        
        return 0.01

