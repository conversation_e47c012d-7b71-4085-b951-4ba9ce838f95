import requests
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import random
import math

class MarketDataProvider:
    def __init__(self):
        # Verwende kostenlose APIs für Marktdaten
        self.alpha_vantage_key = "demo"  # Für <PERSON><PERSON>
        self.base_urls = {
            'alpha_vantage': 'https://www.alphavantage.co/query',
            'finhub': 'https://finnhub.io/api/v1',
            'yahoo': 'https://query1.finance.yahoo.com/v8/finance/chart'
        }
        
    def get_gold_data(self, interval: str = '1min', limit: int = 100) -> List[Dict]:
        """
        Holt aktuelle Gold-USD Daten von verschiedenen Quellen
        """
        try:
            # Versuche zuerst Yahoo Finance (kostenlos und zuverlässig)
            data = self._get_yahoo_data('GC=F', interval, limit)  # Gold Futures
            if data:
                return data
                
            # Fallback zu simulierten Daten für Demo
            return self._generate_realistic_gold_data(limit)
            
        except Exception as e:
            print(f"<PERSON><PERSON> beim Abrufen der Marktdaten: {e}")
            return self._generate_realistic_gold_data(limit)
    
    def _get_yahoo_data(self, symbol: str, interval: str, limit: int) -> List[Dict]:
        """
        Holt Daten von Yahoo Finance
        """
        try:
            # Yahoo Finance Chart API
            period_map = {
                '1min': '1m',
                '5min': '5m',
                '15min': '15m',
                '1hour': '1h',
                '1day': '1d'
            }
            
            yahoo_interval = period_map.get(interval, '1m')
            
            # Berechne Zeitraum
            if interval == '1min':
                period = '1d'  # 1 Tag für 1-Minuten-Daten
            elif interval == '5min':
                period = '5d'  # 5 Tage für 5-Minuten-Daten
            else:
                period = '1mo'  # 1 Monat für längere Intervalle
            
            url = f"{self.base_urls['yahoo']}/{symbol}"
            params = {
                'interval': yahoo_interval,
                'period1': int((datetime.now() - timedelta(days=7)).timestamp()),
                'period2': int(datetime.now().timestamp()),
                'includePrePost': 'false'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'chart' in data and data['chart']['result']:
                    result = data['chart']['result'][0]
                    timestamps = result['timestamp']
                    quotes = result['indicators']['quote'][0]
                    
                    ohlc_data = []
                    for i, ts in enumerate(timestamps):
                        if (quotes['open'][i] is not None and 
                            quotes['high'][i] is not None and
                            quotes['low'][i] is not None and
                            quotes['close'][i] is not None):
                            
                            ohlc_data.append({
                                'timestamp': datetime.fromtimestamp(ts).isoformat(),
                                'open': float(quotes['open'][i]),
                                'high': float(quotes['high'][i]),
                                'low': float(quotes['low'][i]),
                                'close': float(quotes['close'][i]),
                                'volume': float(quotes['volume'][i]) if quotes['volume'][i] else 0.0
                            })
                    
                    # Limitiere auf gewünschte Anzahl
                    return ohlc_data[-limit:] if len(ohlc_data) > limit else ohlc_data
                    
        except Exception as e:
            print(f"Yahoo Finance API Fehler: {e}")
            
        return []
    
    def _generate_realistic_gold_data(self, limit: int) -> List[Dict]:
        """
        Generiert realistische Gold-Preis-Daten für Demo-Zwecke
        """
        # Aktueller Gold-Preis als Basis (ca. 2000 USD/oz)
        base_price = 2000.0
        current_time = datetime.now()
        
        data = []
        price = base_price
        
        for i in range(limit):
            # Simuliere realistische Preisbewegungen
            # Gold hat typischerweise niedrige Volatilität
            volatility = 0.001  # 0.1% Volatilität pro Minute
            
            # Random Walk mit leichtem Trend
            price_change = random.gauss(0, volatility * price)
            
            # Füge etwas Trend hinzu (sehr schwach)
            trend = 0.0001 * math.sin(i / 20)  # Schwacher zyklischer Trend
            price_change += trend * price
            
            # Berechne OHLC für diese "Minute"
            open_price = price
            
            # Simuliere Intrabar-Bewegungen
            high_offset = abs(random.gauss(0, volatility * price * 0.5))
            low_offset = abs(random.gauss(0, volatility * price * 0.5))
            
            high_price = open_price + high_offset
            low_price = open_price - low_offset
            
            # Close-Preis
            close_price = open_price + price_change
            
            # Stelle sicher, dass OHLC-Logik korrekt ist
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            # Simuliere Volumen
            volume = random.randint(100, 1000)
            
            timestamp = current_time - timedelta(minutes=limit-i-1)
            
            data.append({
                'timestamp': timestamp.isoformat(),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': float(volume)
            })
            
            price = close_price
        
        return data
    
    def get_current_price(self) -> float:
        """
        Holt den aktuellen Gold-Preis
        """
        try:
            data = self.get_gold_data(interval='1min', limit=1)
            if data:
                return data[-1]['close']
        except:
            pass
        
        # Fallback zu simuliertem Preis
        return 2000.0 + random.gauss(0, 5)  # Basis-Preis mit etwas Variation
    
    def get_historical_data(self, days: int = 7) -> List[Dict]:
        """
        Holt historische Daten für die letzten N Tage
        """
        try:
            # Für Demo verwenden wir simulierte Daten
            return self._generate_realistic_gold_data(days * 1440)  # 1440 Minuten pro Tag
        except Exception as e:
            print(f"Fehler beim Abrufen historischer Daten: {e}")
            return []

class RealTimeDataStream:
    def __init__(self, market_data_provider: MarketDataProvider):
        self.provider = market_data_provider
        self.subscribers = []
        self.is_running = False
        
    def subscribe(self, callback):
        """Abonniere Echtzeit-Updates"""
        self.subscribers.append(callback)
        
    def start_stream(self):
        """Startet den Echtzeit-Datenstream"""
        self.is_running = True
        
        while self.is_running:
            try:
                # Hole aktuelle Daten
                current_data = self.provider.get_gold_data(interval='1min', limit=1)
                
                if current_data:
                    # Benachrichtige alle Abonnenten
                    for callback in self.subscribers:
                        try:
                            callback(current_data[0])
                        except Exception as e:
                            print(f"Fehler beim Benachrichtigen des Abonnenten: {e}")
                
                # Warte 1 Minute bis zum nächsten Update
                time.sleep(60)
                
            except Exception as e:
                print(f"Fehler im Datenstream: {e}")
                time.sleep(5)  # Kurze Pause bei Fehlern
    
    def stop_stream(self):
        """Stoppt den Echtzeit-Datenstream"""
        self.is_running = False

# Singleton-Instanz für die App
market_data_provider = MarketDataProvider()
data_stream = RealTimeDataStream(market_data_provider)

