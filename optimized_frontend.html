<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gold Trading App - Optimized</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#1e3c72">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js" as="script">
    <link rel="preconnect" href="https://query1.finance.yahoo.com">
    
    <style>
        /* Critical CSS - Inline for faster loading */
        :root {
            --primary-color: #ffd700;
            --secondary-color: #1e3c72;
            --background-dark: #0f1419;
            --card-background: #1a1f2e;
            --text-primary: #ffffff;
            --text-secondary: #b0b8c1;
            --success-color: #00d4aa;
            --error-color: #ff6b6b;
            --warning-color: #ffa726;
            --transition: all 0.2s ease;
            --border-radius: 12px;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--background-dark) 0%, var(--secondary-color) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, var(--primary-color), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(255, 215, 0, 0.1);
            transition: var(--transition);
            will-change: transform;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
        }

        .card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .price-display {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            text-align: center;
            margin: 20px 0;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(45deg, var(--primary-color), #ffed4e);
            color: var(--background-dark);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin: 5px;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
            text-align: center;
        }

        .status.loading {
            background: rgba(255, 167, 38, 0.2);
            color: var(--warning-color);
        }

        .status.success {
            background: rgba(0, 212, 170, 0.2);
            color: var(--success-color);
        }

        .status.error {
            background: rgba(255, 107, 107, 0.2);
            color: var(--error-color);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 215, 0, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .signal-card {
            background: linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(0, 212, 170, 0.05));
            border: 2px solid var(--success-color);
        }

        .signal-card.sell {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
            border-color: var(--error-color);
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .metric {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .price-display {
                font-size: 2rem;
            }
        }

        /* Performance optimizations */
        .card, .btn {
            contain: layout style paint;
        }

        .chart-container canvas {
            will-change: auto;
        }

        /* Reduce motion for users who prefer it */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🥇 Gold Trading App</h1>
            <p>Professionelle ICT-Analyse für XAUUSD</p>
            <div id="connectionStatus" class="status">🔄 Verbinde...</div>
        </div>

        <div class="grid">
            <!-- Current Price Card -->
            <div class="card">
                <h3>💰 Aktueller Gold-Preis</h3>
                <div id="currentPrice" class="price-display">$0.00</div>
                <div class="metrics">
                    <div class="metric">
                        <div id="priceChange" class="metric-value">+0.00</div>
                        <div class="metric-label">Änderung</div>
                    </div>
                    <div class="metric">
                        <div id="priceChangePercent" class="metric-value">+0.00%</div>
                        <div class="metric-label">Prozent</div>
                    </div>
                </div>
                <button class="btn" onclick="refreshData()" id="refreshBtn">🔄 Aktualisieren</button>
            </div>

            <!-- Chart Card -->
            <div class="card">
                <h3>📈 Preis-Chart</h3>
                <div class="chart-container">
                    <canvas id="priceChart"></canvas>
                </div>
            </div>

            <!-- Trading Controls -->
            <div class="card">
                <h3>⚡ Trading-Kontrollen</h3>
                <div class="metrics">
                    <div class="metric">
                        <div id="accountBalance" class="metric-value">$100</div>
                        <div class="metric-label">Kontostand</div>
                    </div>
                    <div class="metric">
                        <div id="activeTrades" class="metric-value">0</div>
                        <div class="metric-label">Aktive Trades</div>
                    </div>
                </div>
                <button class="btn" onclick="runAnalysis()" id="analyzeBtn">🧠 ICT-Analyse</button>
                <button class="btn" onclick="autoAnalyze()" id="autoBtn">⚡ Auto-Analyse</button>
            </div>

            <!-- Trading Signal -->
            <div class="card" id="signalCard" style="display: none;">
                <h3>🎯 Trading-Signal</h3>
                <div id="signalContent"></div>
                <button class="btn" onclick="executeSignal()" id="executeBtn">🚀 Trade ausführen</button>
            </div>
        </div>

        <!-- Analysis Results -->
        <div class="card" id="analysisCard" style="display: none;">
            <h3>🧠 ICT-Analyse Ergebnisse</h3>
            <div id="analysisContent"></div>
        </div>
    </div>

    <!-- Load Chart.js asynchronously -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js" async></script>
    
    <script>
        // Performance optimized JavaScript
        class TradingApp {
            constructor() {
                this.chart = null;
                this.currentData = null;
                this.isLoading = false;
                this.cache = new Map();
                this.lastUpdate = 0;
                this.updateInterval = 30000; // 30 seconds
                
                this.init();
            }

            async init() {
                await this.waitForChart();
                this.initializeChart();
                this.setupEventListeners();
                await this.refreshData();
                this.startAutoUpdate();
                this.updateConnectionStatus('success', '✅ Verbunden');
            }

            waitForChart() {
                return new Promise((resolve) => {
                    if (window.Chart) {
                        resolve();
                    } else {
                        const checkChart = () => {
                            if (window.Chart) {
                                resolve();
                            } else {
                                setTimeout(checkChart, 100);
                            }
                        };
                        checkChart();
                    }
                });
            }

            setupEventListeners() {
                // Optimize event listeners with passive option where possible
                window.addEventListener('online', () => this.handleOnline(), { passive: true });
                window.addEventListener('offline', () => this.handleOffline(), { passive: true });
                
                // Visibility API for performance optimization
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.pauseUpdates();
                    } else {
                        this.resumeUpdates();
                    }
                }, { passive: true });
            }

            initializeChart() {
                const ctx = document.getElementById('priceChart').getContext('2d');
                this.chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Gold Preis (USD)',
                            data: [],
                            borderColor: '#ffd700',
                            backgroundColor: 'rgba(255, 215, 0, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 0, // Hide points for better performance
                            pointHoverRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: {
                            duration: 300 // Reduced animation time
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                display: false // Hide legend for cleaner look
                            }
                        },
                        scales: {
                            x: {
                                display: false // Hide x-axis for cleaner look
                            },
                            y: {
                                ticks: {
                                    color: '#b0b8c1',
                                    callback: function(value) {
                                        return '$' + value.toFixed(2);
                                    }
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            async refreshData() {
                if (this.isLoading) return;

                this.isLoading = true;
                this.setButtonState('refreshBtn', true);
                this.updateConnectionStatus('loading', '📡 Lade Daten...');

                try {
                    const cacheKey = 'market-data';
                    const now = Date.now();

                    // Check cache first
                    if (this.cache.has(cacheKey) && (now - this.lastUpdate) < 30000) {
                        const cachedData = this.cache.get(cacheKey);
                        this.updateUI(cachedData);
                        this.updateConnectionStatus('success', '✅ Daten aktualisiert (Cache)');
                        return;
                    }

                    const response = await fetch('/api/market-data?limit=50');
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);

                    const data = await response.json();

                    // Cache the data
                    this.cache.set(cacheKey, data);
                    this.lastUpdate = now;

                    this.updateUI(data);
                    this.updateConnectionStatus('success', '✅ Daten aktualisiert');

                } catch (error) {
                    console.error('Data refresh error:', error);
                    this.updateConnectionStatus('error', '❌ Fehler beim Laden');

                    // Try to use cached data as fallback
                    const cachedData = this.cache.get('market-data');
                    if (cachedData) {
                        this.updateUI(cachedData);
                        this.updateConnectionStatus('warning', '⚠️ Offline-Daten');
                    }
                } finally {
                    this.isLoading = false;
                    this.setButtonState('refreshBtn', false);
                }
            }

            updateUI(data) {
                if (!data || !data.data || data.data.length === 0) return;

                this.currentData = data;
                const latestPrice = data.data[data.data.length - 1];

                // Update price display with animation
                this.updatePriceDisplay(latestPrice);

                // Update chart efficiently
                this.updateChart(data.data);
            }

            updatePriceDisplay(priceData) {
                const priceElement = document.getElementById('currentPrice');
                const changeElement = document.getElementById('priceChange');
                const percentElement = document.getElementById('priceChangePercent');

                const currentPrice = priceData.close;
                const change = currentPrice - (priceData.open || currentPrice);
                const changePercent = ((change / (priceData.open || currentPrice)) * 100);

                // Animate price change
                priceElement.style.transform = 'scale(1.05)';
                priceElement.textContent = `$${currentPrice.toFixed(2)}`;

                setTimeout(() => {
                    priceElement.style.transform = 'scale(1)';
                }, 200);

                // Update change indicators
                const isPositive = change >= 0;
                const changeColor = isPositive ? '#00d4aa' : '#ff6b6b';
                const changePrefix = isPositive ? '+' : '';

                changeElement.textContent = `${changePrefix}${change.toFixed(2)}`;
                changeElement.style.color = changeColor;

                percentElement.textContent = `${changePrefix}${changePercent.toFixed(2)}%`;
                percentElement.style.color = changeColor;
            }

            updateChart(data) {
                if (!this.chart || !data.length) return;

                // Limit data points for better performance
                const maxPoints = 50;
                const chartData = data.slice(-maxPoints);

                const labels = chartData.map((_, index) => index);
                const prices = chartData.map(item => item.close);

                // Update chart data efficiently
                this.chart.data.labels = labels;
                this.chart.data.datasets[0].data = prices;

                // Update with minimal animation
                this.chart.update('none');
            }

            async runAnalysis() {
                if (this.isLoading) return;

                this.isLoading = true;
                this.setButtonState('analyzeBtn', true);
                this.updateConnectionStatus('loading', '🧠 Analysiere...');

                try {
                    const response = await fetch('/api/ict-analysis');
                    if (!response.ok) throw new Error(`HTTP ${response.status}`);

                    const analysis = await response.json();

                    this.displayAnalysis(analysis);

                    if (analysis.trading_signal) {
                        this.displayTradingSignal(analysis.trading_signal);
                    }

                    this.updateConnectionStatus('success', '✅ Analyse abgeschlossen');

                } catch (error) {
                    console.error('Analysis error:', error);
                    this.updateConnectionStatus('error', '❌ Analyse fehlgeschlagen');
                } finally {
                    this.isLoading = false;
                    this.setButtonState('analyzeBtn', false);
                }
            }

            async autoAnalyze() {
                await this.runAnalysis();
                // Auto-execute if signal confidence is high
                const signalCard = document.getElementById('signalCard');
                if (signalCard.style.display !== 'none') {
                    const confidence = parseFloat(signalCard.dataset.confidence || 0);
                    if (confidence > 0.8) {
                        setTimeout(() => this.executeSignal(), 2000);
                    }
                }
            }

            displayAnalysis(analysis) {
                const analysisCard = document.getElementById('analysisCard');
                const analysisContent = document.getElementById('analysisContent');

                let html = '<div class="metrics">';

                // Market Structure
                if (analysis.market_structure) {
                    const ms = analysis.market_structure;
                    html += `
                        <div class="metric">
                            <div class="metric-value">${ms.trend || 'Sideways'}</div>
                            <div class="metric-label">Trend</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${(ms.confidence * 100).toFixed(0)}%</div>
                            <div class="metric-label">Konfidenz</div>
                        </div>
                    `;
                }

                // Fair Value Gaps
                if (analysis.fair_value_gaps && analysis.fair_value_gaps.length > 0) {
                    const activeGaps = analysis.fair_value_gaps.filter(gap => !gap.filled);
                    html += `
                        <div class="metric">
                            <div class="metric-value">${activeGaps.length}</div>
                            <div class="metric-label">Aktive FVGs</div>
                        </div>
                    `;
                }

                // Premium/Discount
                if (analysis.premium_discount) {
                    const pd = analysis.premium_discount;
                    html += `
                        <div class="metric">
                            <div class="metric-value">${pd.zone || 'Neutral'}</div>
                            <div class="metric-label">Zone</div>
                        </div>
                    `;
                }

                html += '</div>';

                analysisContent.innerHTML = html;
                analysisCard.style.display = 'block';
            }

            displayTradingSignal(signal) {
                const signalCard = document.getElementById('signalCard');
                const signalContent = document.getElementById('signalContent');

                const isBuy = signal.signal_type === 'BUY';
                signalCard.className = `card signal-card ${isBuy ? '' : 'sell'}`;
                signalCard.dataset.confidence = signal.confidence;

                const html = `
                    <div class="metrics">
                        <div class="metric">
                            <div class="metric-value" style="color: ${isBuy ? '#00d4aa' : '#ff6b6b'}">
                                ${signal.signal_type}
                            </div>
                            <div class="metric-label">Signal</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">$${signal.entry_price}</div>
                            <div class="metric-label">Entry</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">$${signal.stop_loss}</div>
                            <div class="metric-label">Stop Loss</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">$${signal.take_profit}</div>
                            <div class="metric-label">Take Profit</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">${(signal.confidence * 100).toFixed(0)}%</div>
                            <div class="metric-label">Konfidenz</div>
                        </div>
                    </div>
                    <p style="margin-top: 15px; font-size: 0.9rem; color: var(--text-secondary);">
                        ${signal.reasoning || 'Automatisch generiertes Signal basierend auf ICT-Analyse'}
                    </p>
                `;

                signalContent.innerHTML = html;
                signalCard.style.display = 'block';
            }

            executeSignal() {
                this.updateConnectionStatus('loading', '🚀 Führe Trade aus...');

                // Simulate trade execution
                setTimeout(() => {
                    const activeTradesElement = document.getElementById('activeTrades');
                    const currentTrades = parseInt(activeTradesElement.textContent);
                    activeTradesElement.textContent = currentTrades + 1;

                    document.getElementById('signalCard').style.display = 'none';
                    this.updateConnectionStatus('success', '✅ Trade ausgeführt');
                }, 1500);
            }

            setButtonState(buttonId, disabled) {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.disabled = disabled;
                    if (disabled) {
                        button.innerHTML = button.innerHTML.replace(/^[^<]*/, '<span class="loading-spinner"></span> ');
                    } else {
                        button.innerHTML = button.innerHTML.replace(/<span class="loading-spinner"><\/span>\s*/, '');
                    }
                }
            }

            updateConnectionStatus(type, message) {
                const statusElement = document.getElementById('connectionStatus');
                statusElement.className = `status ${type}`;
                statusElement.textContent = message;
            }

            startAutoUpdate() {
                setInterval(() => {
                    if (!document.hidden && navigator.onLine) {
                        this.refreshData();
                    }
                }, this.updateInterval);
            }

            pauseUpdates() {
                console.log('Pausing updates - tab hidden');
            }

            resumeUpdates() {
                console.log('Resuming updates - tab visible');
                this.refreshData();
            }

            handleOnline() {
                this.updateConnectionStatus('success', '✅ Verbindung wiederhergestellt');
                this.refreshData();
            }

            handleOffline() {
                this.updateConnectionStatus('error', '❌ Offline');
            }
        }

        // Global functions for button clicks
        let app;

        document.addEventListener('DOMContentLoaded', () => {
            app = new TradingApp();
        });

        function refreshData() {
            if (app) app.refreshData();
        }

        function runAnalysis() {
            if (app) app.runAnalysis();
        }

        function autoAnalyze() {
            if (app) app.autoAnalyze();
        }

        function executeSignal() {
            if (app) app.executeSignal();
        }

        // Register service worker for PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
