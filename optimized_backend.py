import os
import time
import json
import gzip
from flask import Flask, jsonify, request, send_from_directory, Response
from flask_cors import CORS
from datetime import datetime, timedelta
from functools import wraps
import threading
import logging

# Import optimized modules
from improved_market_data import ImprovedMarketDataProvider
from enhanced_ict_analyzer import <PERSON>hancedIC<PERSON>naly<PERSON>

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize optimized providers
market_data_provider = ImprovedMarketDataProvider()
ict_analyzer = EnhancedICTAnalyzer()

# Performance optimization: In-memory cache with TTL
class PerformanceCache:
    def __init__(self):
        self.cache = {}
        self.lock = threading.RLock()
        
    def get(self, key):
        with self.lock:
            if key in self.cache:
                data, timestamp, ttl = self.cache[key]
                if time.time() - timestamp < ttl:
                    return data
                else:
                    del self.cache[key]
            return None
    
    def set(self, key, data, ttl=60):
        with self.lock:
            self.cache[key] = (data, time.time(), ttl)
    
    def clear_expired(self):
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, (_, timestamp, ttl) in self.cache.items()
                if current_time - timestamp >= ttl
            ]
            for key in expired_keys:
                del self.cache[key]

# Global cache instance
cache = PerformanceCache()

# Performance monitoring
class PerformanceMonitor:
    def __init__(self):
        self.request_times = []
        self.request_count = 0
        self.error_count = 0
        self.start_time = time.time()
        
    def record_request(self, duration, success=True):
        self.request_times.append(duration)
        self.request_count += 1
        if not success:
            self.error_count += 1
        
        # Keep only last 100 requests for memory efficiency
        if len(self.request_times) > 100:
            self.request_times = self.request_times[-100:]
    
    def get_stats(self):
        if not self.request_times:
            return {
                'avg_response_time': 0,
                'request_count': 0,
                'error_rate': 0,
                'uptime': time.time() - self.start_time
            }
        
        return {
            'avg_response_time': sum(self.request_times) / len(self.request_times),
            'request_count': self.request_count,
            'error_rate': self.error_count / self.request_count if self.request_count > 0 else 0,
            'uptime': time.time() - self.start_time,
            'cache_size': len(cache.cache)
        }

monitor = PerformanceMonitor()

# Decorators for performance optimization
def cache_response(ttl=60):
    """Cache API responses for specified TTL"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{f.__name__}_{hash(str(request.args))}"
            
            # Try to get from cache first
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.info(f"Cache hit for {f.__name__}")
                return cached_result
            
            # Execute function and cache result
            start_time = time.time()
            try:
                result = f(*args, **kwargs)
                cache.set(cache_key, result, ttl)
                
                duration = time.time() - start_time
                monitor.record_request(duration, True)
                logger.info(f"API call {f.__name__} completed in {duration:.3f}s")
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                monitor.record_request(duration, False)
                logger.error(f"API call {f.__name__} failed in {duration:.3f}s: {e}")
                raise
                
        return decorated_function
    return decorator

def compress_response(f):
    """Compress large responses with gzip"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)
        
        # Only compress if client accepts gzip and response is large enough
        if ('gzip' in request.headers.get('Accept-Encoding', '') and 
            hasattr(response, 'data') and len(response.data) > 1000):
            
            compressed_data = gzip.compress(response.data)
            response.data = compressed_data
            response.headers['Content-Encoding'] = 'gzip'
            response.headers['Content-Length'] = len(compressed_data)
            
        return response
    return decorated_function

# Optimized API endpoints
@app.route('/api/market-data')
@cache_response(ttl=30)  # Cache for 30 seconds
@compress_response
def api_market_data():
    """Optimized market data API with caching and compression"""
    try:
        # Get parameters
        limit = min(int(request.args.get('limit', 50)), 200)  # Limit max data points
        
        # Get market data
        historical_data = market_data_provider.get_historical_data(days=1, interval='1m')
        current_price_data = market_data_provider.get_current_price()
        market_status = market_data_provider.get_market_status()
        
        # Format response efficiently
        if historical_data:
            # Take only the requested number of recent data points
            recent_data = historical_data[-limit:]
            
            # Optimize data structure for frontend
            chart_data = [
                {
                    'timestamp': item['timestamp'],
                    'open': item['open'],
                    'high': item['high'],
                    'low': item['low'],
                    'close': item['close'],
                    'volume': item['volume']
                }
                for item in recent_data
            ]
        else:
            chart_data = []
        
        response_data = {
            'data': chart_data,
            'current_price': current_price_data['price'],
            'price_change': current_price_data['change'],
            'price_change_percent': current_price_data['change_percent'],
            'market_status': market_status,
            'timestamp': datetime.utcnow().isoformat(),
            'source': current_price_data['source'],
            'data_points': len(chart_data)
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Market data API error: {e}")
        return jsonify({
            'error': 'Market data unavailable', 
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@app.route('/api/ict-analysis')
@cache_response(ttl=120)  # Cache for 2 minutes
@compress_response
def api_ict_analysis():
    """Optimized ICT analysis API with enhanced caching"""
    try:
        # Get historical data for analysis
        historical_data = market_data_provider.get_historical_data(days=2, interval='1m')
        
        if not historical_data or len(historical_data) < 20:
            return jsonify({
                'error': 'Insufficient data for analysis',
                'data_points': len(historical_data) if historical_data else 0
            }), 400
        
        # Perform enhanced ICT analysis
        analysis = ict_analyzer.analyze_market_data(historical_data)
        
        # Optimize response size by removing unnecessary data
        optimized_analysis = {
            'market_structure': {
                'trend': analysis['market_structure']['trend'],
                'strength': analysis['market_structure']['strength'],
                'confidence': analysis['market_structure']['confidence']
            },
            'fair_value_gaps': [
                {
                    'type': gap['type'],
                    'start_price': gap['start_price'],
                    'end_price': gap['end_price'],
                    'strength': gap['strength'],
                    'filled': gap['filled']
                }
                for gap in analysis['fair_value_gaps'][:5]  # Limit to top 5
            ],
            'liquidity_pools': [
                {
                    'price': pool['price'],
                    'type': pool['type'],
                    'strength': pool['strength'],
                    'swept': pool['swept']
                }
                for pool in analysis['liquidity_pools'][:5]  # Limit to top 5
            ],
            'premium_discount': analysis['premium_discount'],
            'trading_signal': analysis['trading_signal'],
            'confidence_score': analysis['confidence_score'],
            'analysis_timestamp': analysis['analysis_timestamp'],
            'analyzer_version': analysis['analyzer_version']
        }
        
        return jsonify(optimized_analysis)
        
    except Exception as e:
        logger.error(f"ICT analysis API error: {e}")
        return jsonify({
            'error': 'Analysis failed', 
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@app.route('/api/performance')
def api_performance():
    """Performance monitoring endpoint"""
    try:
        stats = monitor.get_stats()
        
        # Add cache statistics
        cache.clear_expired()  # Clean up expired entries
        stats['cache_hit_rate'] = 'N/A'  # Would need more sophisticated tracking
        
        return jsonify({
            'performance': stats,
            'status': 'healthy' if stats['error_rate'] < 0.1 else 'degraded',
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Performance API error: {e}")
        return jsonify({
            'error': 'Performance data unavailable',
            'message': str(e)
        }), 500

@app.route('/api/health')
def api_health():
    """Health check endpoint for monitoring"""
    try:
        # Quick health checks
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'uptime': time.time() - monitor.start_time,
            'version': '2.0.0'
        }
        
        # Test market data provider
        try:
            test_price = market_data_provider.get_current_price()
            health_status['market_data'] = 'ok' if test_price else 'degraded'
        except:
            health_status['market_data'] = 'error'
            health_status['status'] = 'degraded'
        
        # Test ICT analyzer
        try:
            # Quick test with minimal data
            test_data = [{'open': 2000, 'high': 2001, 'low': 1999, 'close': 2000.5, 'timestamp': datetime.utcnow().isoformat(), 'volume': 1000}] * 25
            ict_analyzer.analyze_market_data(test_data)
            health_status['ict_analyzer'] = 'ok'
        except:
            health_status['ict_analyzer'] = 'error'
            health_status['status'] = 'degraded'
        
        return jsonify(health_status)
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

# Serve optimized frontend
@app.route('/')
def serve_optimized_frontend():
    """Serve the optimized frontend"""
    try:
        return send_from_directory('.', 'optimized_frontend.html')
    except:
        return send_from_directory('.', 'index.html')  # Fallback

@app.route('/manifest.json')
def serve_manifest():
    """Serve PWA manifest"""
    try:
        return send_from_directory('.', 'manifest.json')
    except:
        return jsonify({
            "name": "Gold Trading App",
            "short_name": "GoldTrader",
            "start_url": "/",
            "display": "standalone",
            "theme_color": "#1e3c72",
            "background_color": "#0f1419"
        })

@app.route('/sw.js')
def serve_service_worker():
    """Serve service worker"""
    try:
        return send_from_directory('.', 'sw.js')
    except:
        return Response("// Service worker not found", mimetype='application/javascript')

# Background task to clean cache periodically
def cleanup_cache():
    """Background task to clean expired cache entries"""
    while True:
        time.sleep(300)  # Run every 5 minutes
        cache.clear_expired()
        logger.info(f"Cache cleanup completed. Current cache size: {len(cache.cache)}")

# Start background cleanup thread
cleanup_thread = threading.Thread(target=cleanup_cache, daemon=True)
cleanup_thread.start()

if __name__ == '__main__':
    logger.info("Starting optimized Gold Trading App...")
    app.run(host='0.0.0.0', port=5001, debug=False, threaded=True)
