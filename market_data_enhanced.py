import requests
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import random
import math

class EnhancedMarketDataProvider:
    def __init__(self):
        self.api_keys = {
            'alpha_vantage': 'demo',  # Ersetzen mit echtem API Key
            'yahoo_finance': None,    # Kein Key erforderlich
            'twelve_data': 'demo'     # Ersetzen mit echtem API Key
        }
        
        self.base_urls = {
            'alpha_vantage': 'https://www.alphavantage.co/query',
            'yahoo_finance': 'https://query1.finance.yahoo.com/v8/finance/chart',
            'twelve_data': 'https://api.twelvedata.com'
        }
        
        self.cache = {}
        self.cache_duration = 60  # Cache für 60 Sekunden
        
    def get_gold_data(self, interval: str = '1min', limit: int = 100) -> List[Dict]:
        """
        Holt Gold-Daten mit mehreren Fallback-Quellen
        """
        cache_key = f"gold_{interval}_{limit}"
        
        # Prüfe Cache
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        # Versuche verschiedene Datenquellen
        data_sources = [
            self._get_yahoo_finance_data,
            self._get_alpha_vantage_data,
            self._get_twelve_data,
            self._get_enhanced_simulated_data
        ]
        
        for source in data_sources:
            try:
                data = source(interval, limit)
                if data and len(data) > 0:
                    # Cache erfolgreiche Daten
                    self.cache[cache_key] = {
                        'data': data,
                        'timestamp': datetime.utcnow()
                    }
                    return data
            except Exception as e:
                print(f"Datenquelle fehlgeschlagen: {e}")
                continue
        
        # Fallback zu simulierten Daten
        return self._get_enhanced_simulated_data(interval, limit)
    
    def _get_yahoo_finance_data(self, interval: str, limit: int) -> List[Dict]:
        """
        Holt Daten von Yahoo Finance
        """
        # Yahoo Finance Symbol für Gold
        symbol = 'GC=F'  # Gold Futures
        
        # Zeitraum berechnen
        end_time = int(datetime.utcnow().timestamp())
        start_time = end_time - (limit * 60)  # Annahme: 1min Intervall
        
        url = f"{self.base_urls['yahoo_finance']}/{symbol}"
        params = {
            'period1': start_time,
            'period2': end_time,
            'interval': '1m',
            'includePrePost': 'false'
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('chart', {}).get('result', [])
            
            if result:
                timestamps = result[0].get('timestamp', [])
                quote_data = result[0].get('indicators', {}).get('quote', [{}])[0]
                
                ohlc_data = []
                for i, timestamp in enumerate(timestamps):
                    if i < len(quote_data.get('open', [])):
                        ohlc_data.append({
                            'timestamp': datetime.fromtimestamp(timestamp).isoformat(),
                            'open': quote_data['open'][i] or 2000.0,
                            'high': quote_data['high'][i] or 2005.0,
                            'low': quote_data['low'][i] or 1995.0,
                            'close': quote_data['close'][i] or 2000.0,
                            'volume': quote_data.get('volume', [1000])[i] or 1000
                        })
                
                return ohlc_data[-limit:] if len(ohlc_data) > limit else ohlc_data
        
        raise Exception("Yahoo Finance API Fehler")
    
    def _get_alpha_vantage_data(self, interval: str, limit: int) -> List[Dict]:
        """
        Holt Daten von Alpha Vantage
        """
        if self.api_keys['alpha_vantage'] == 'demo':
            raise Exception("Alpha Vantage API Key erforderlich")
        
        url = self.base_urls['alpha_vantage']
        params = {
            'function': 'TIME_SERIES_INTRADAY',
            'symbol': 'XAUUSD',
            'interval': '1min',
            'apikey': self.api_keys['alpha_vantage'],
            'outputsize': 'compact'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            time_series = data.get('Time Series (1min)', {})
            
            ohlc_data = []
            for timestamp, values in time_series.items():
                ohlc_data.append({
                    'timestamp': datetime.fromisoformat(timestamp).isoformat(),
                    'open': float(values['1. open']),
                    'high': float(values['2. high']),
                    'low': float(values['3. low']),
                    'close': float(values['4. close']),
                    'volume': int(values['5. volume'])
                })
            
            # Sortiere nach Zeitstempel
            ohlc_data.sort(key=lambda x: x['timestamp'])
            return ohlc_data[-limit:] if len(ohlc_data) > limit else ohlc_data
        
        raise Exception("Alpha Vantage API Fehler")
    
    def _get_twelve_data(self, interval: str, limit: int) -> List[Dict]:
        """
        Holt Daten von Twelve Data
        """
        if self.api_keys['twelve_data'] == 'demo':
            raise Exception("Twelve Data API Key erforderlich")
        
        url = f"{self.base_urls['twelve_data']}/time_series"
        params = {
            'symbol': 'XAUUSD',
            'interval': '1min',
            'outputsize': limit,
            'apikey': self.api_keys['twelve_data']
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            values = data.get('values', [])
            
            ohlc_data = []
            for item in values:
                ohlc_data.append({
                    'timestamp': datetime.fromisoformat(item['datetime']).isoformat(),
                    'open': float(item['open']),
                    'high': float(item['high']),
                    'low': float(item['low']),
                    'close': float(item['close']),
                    'volume': int(item.get('volume', 1000))
                })
            
            return ohlc_data
        
        raise Exception("Twelve Data API Fehler")
    
    def _get_enhanced_simulated_data(self, interval: str, limit: int) -> List[Dict]:
        """
        Generiert verbesserte realistische Marktdaten
        """
        current_time = datetime.utcnow()
        base_price = 2000.0 + random.gauss(0, 20)  # Basis um $2000 mit Variation
        
        # Marktzeiten berücksichtigen (vereinfacht)
        hour = current_time.hour
        is_active_session = 8 <= hour <= 17 or 22 <= hour <= 23  # London/NY Sessions
        
        data = []
        price = base_price
        
        for i in range(limit):
            timestamp = current_time - timedelta(minutes=limit-i-1)
            
            # Angepasste Volatilität basierend auf Marktzeiten
            if is_active_session:
                volatility = 0.0008  # Höhere Volatilität während aktiver Sessions
                volume_base = 800
            else:
                volatility = 0.0003  # Niedrigere Volatilität außerhalb
                volume_base = 300
            
            # Realistische Preisbewegung mit Trends
            trend_factor = math.sin(i / 30) * 0.0002  # Langfristiger Trend
            noise = random.gauss(0, volatility * price)
            
            # Momentum-Effekt (Preise tendieren in Richtung der letzten Bewegung)
            if i > 0:
                last_change = data[-1]['close'] - (data[-1]['open'] if len(data) == 1 else data[-2]['close'])
                momentum = last_change * 0.3  # 30% Momentum-Carry-Over
            else:
                momentum = 0
            
            price_change = trend_factor * price + noise + momentum
            
            # OHLC für diese Periode
            open_price = price
            close_price = price + price_change
            
            # Realistische High/Low basierend auf Volatilität
            intrabar_range = abs(random.gauss(0, volatility * price * 0.8))
            high_price = max(open_price, close_price) + intrabar_range * random.uniform(0.3, 1.0)
            low_price = min(open_price, close_price) - intrabar_range * random.uniform(0.3, 1.0)
            
            # Volumen mit realistischen Spitzen
            volume_multiplier = 1.0
            if abs(price_change) > volatility * price * 1.5:  # Große Bewegung
                volume_multiplier = random.uniform(2.0, 4.0)
            
            volume = int(volume_base * volume_multiplier * random.uniform(0.7, 1.3))
            
            data.append({
                'timestamp': timestamp.isoformat(),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
            
            price = close_price
        
        return data
    
    def get_current_price(self) -> float:
        """
        Holt aktuellen Gold-Preis
        """
        try:
            # Versuche echte Daten zu holen
            data = self.get_gold_data(limit=1)
            if data:
                return data[-1]['close']
        except:
            pass
        
        # Fallback zu simuliertem Preis
        base_price = 2000.0
        variation = random.gauss(0, 10)
        return round(base_price + variation, 2)
    
    def get_historical_data(self, days: int = 7) -> List[Dict]:
        """
        Holt historische Daten für mehrere Tage
        """
        # Für Demo: Generiere Daten für mehrere Tage
        all_data = []
        current_time = datetime.utcnow()
        
        for day in range(days):
            day_start = current_time - timedelta(days=days-day)
            day_data = self._generate_daily_data(day_start)
            all_data.extend(day_data)
        
        return all_data
    
    def _generate_daily_data(self, start_date: datetime) -> List[Dict]:
        """
        Generiert realistische Tagesdaten
        """
        data = []
        base_price = 2000.0 + random.gauss(0, 15)
        
        # 24 Stunden * 60 Minuten = 1440 Datenpunkte pro Tag
        for minute in range(1440):
            timestamp = start_date + timedelta(minutes=minute)
            
            # Marktaktivität basierend auf Tageszeit
            hour = timestamp.hour
            if 8 <= hour <= 17:  # Haupthandelszeiten
                volatility = 0.0008
                volume_base = 800
            elif 22 <= hour <= 23 or 0 <= hour <= 2:  # Asiatische Session
                volatility = 0.0005
                volume_base = 500
            else:  # Ruhige Zeiten
                volatility = 0.0002
                volume_base = 200
            
            # Preisbewegung
            price_change = random.gauss(0, volatility * base_price)
            
            # OHLC
            open_price = base_price
            close_price = base_price + price_change
            
            range_size = abs(random.gauss(0, volatility * base_price * 0.6))
            high_price = max(open_price, close_price) + range_size * random.uniform(0, 0.8)
            low_price = min(open_price, close_price) - range_size * random.uniform(0, 0.8)
            
            volume = int(volume_base * random.uniform(0.5, 2.0))
            
            data.append({
                'timestamp': timestamp.isoformat(),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
            
            base_price = close_price
        
        return data
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """
        Prüft ob Cache-Eintrag noch gültig ist
        """
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        age = (datetime.utcnow() - cache_time).total_seconds()
        
        return age < self.cache_duration
    
    def get_market_status(self) -> Dict:
        """
        Gibt aktuellen Marktstatus zurück
        """
        current_time = datetime.utcnow()
        hour = current_time.hour
        
        # Vereinfachte Marktzeiten (UTC)
        if 8 <= hour <= 17:
            session = "London/NY"
            activity = "Hoch"
        elif 22 <= hour <= 23 or 0 <= hour <= 2:
            session = "Asien"
            activity = "Mittel"
        else:
            session = "Ruhig"
            activity = "Niedrig"
        
        return {
            'current_time': current_time.isoformat(),
            'session': session,
            'activity_level': activity,
            'market_open': True,  # Gold handelt 24/5
            'next_major_session': self._get_next_session(hour)
        }
    
    def _get_next_session(self, current_hour: int) -> str:
        """
        Bestimmt nächste wichtige Handelssession
        """
        if current_hour < 8:
            return "London (08:00 UTC)"
        elif current_hour < 13:
            return "NY (13:00 UTC)"
        elif current_hour < 22:
            return "Asien (22:00 UTC)"
        else:
            return "London (08:00 UTC nächster Tag)"
    
    def get_volatility_metrics(self, data: List[Dict]) -> Dict:
        """
        Berechnet Volatilitäts-Metriken
        """
        if len(data) < 20:
            return {'atr': 0, 'volatility': 0, 'range_percentage': 0}
        
        # Average True Range (ATR)
        true_ranges = []
        for i in range(1, len(data)):
            high_low = data[i]['high'] - data[i]['low']
            high_close = abs(data[i]['high'] - data[i-1]['close'])
            low_close = abs(data[i]['low'] - data[i-1]['close'])
            
            true_range = max(high_low, high_close, low_close)
            true_ranges.append(true_range)
        
        atr = sum(true_ranges[-14:]) / 14 if len(true_ranges) >= 14 else sum(true_ranges) / len(true_ranges)
        
        # Preisvolatilität (Standardabweichung der Returns)
        returns = []
        for i in range(1, len(data)):
            ret = (data[i]['close'] - data[i-1]['close']) / data[i-1]['close']
            returns.append(ret)
        
        if returns:
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = math.sqrt(variance) * 100  # In Prozent
        else:
            volatility = 0
        
        # Tagesrange in Prozent
        latest = data[-1]
        range_percentage = ((latest['high'] - latest['low']) / latest['close']) * 100
        
        return {
            'atr': round(atr, 2),
            'volatility': round(volatility, 4),
            'range_percentage': round(range_percentage, 2),
            'trend_strength': self._calculate_trend_strength(data)
        }
    
    def _calculate_trend_strength(self, data: List[Dict]) -> float:
        """
        Berechnet Trendstärke basierend auf Preisbewegung
        """
        if len(data) < 20:
            return 0.0
        
        # Vergleiche aktuelle Position mit Moving Average
        recent_prices = [d['close'] for d in data[-20:]]
        ma = sum(recent_prices) / len(recent_prices)
        current_price = data[-1]['close']
        
        # Trendrichtung und Stärke
        trend_direction = 1 if current_price > ma else -1
        trend_strength = abs(current_price - ma) / ma
        
        return round(trend_direction * min(trend_strength * 10, 1.0), 2)

# Globale Instanz für einfache Verwendung
enhanced_market_data_provider = EnhancedMarketDataProvider()

