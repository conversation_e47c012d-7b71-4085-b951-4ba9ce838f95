import random
import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

@dataclass
class PriceLevel:
    price: float
    timestamp: datetime
    level_type: str  # 'support', 'resistance', 'high', 'low'
    strength: float  # 0.0 to 1.0

@dataclass
class FairValueGap:
    start_price: float
    end_price: float
    timestamp: datetime
    gap_type: str  # 'bullish', 'bearish'
    filled: bool = False

@dataclass
class LiquidityPool:
    price: float
    timestamp: datetime
    pool_type: str  # 'buy_stops', 'sell_stops'
    strength: float  # 0.0 to 1.0

@dataclass
class MarketStructure:
    trend: str  # 'bullish', 'bearish', 'sideways'
    higher_highs: List[PriceLevel]
    higher_lows: List[PriceLevel]
    lower_highs: List[PriceLevel]
    lower_lows: List[PriceLevel]
    key_levels: List[PriceLevel]

class ICTAnalyzer:
    def __init__(self):
        self.lookback_periods = 50  # An<PERSON>hl der Kerzen für Analyse
        self.gap_threshold = 0.0005  # Mindest-Gap-Größe (0.05%)
        self.liquidity_threshold = 0.001  # Mindest-Bewegung für Liquiditäts-Erkennung
        
    def analyze_market_data(self, ohlc_data: List[Dict]) -> Dict:
        """
        Hauptanalyse-Funktion die alle ICT-Konzepte anwendet
        """
        if len(ohlc_data) < self.lookback_periods:
            return self._empty_analysis()
            
        # Konvertiere zu einfacher Datenstruktur
        data = []
        for item in ohlc_data:
            data.append({
                'timestamp': datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00')) if isinstance(item['timestamp'], str) else item['timestamp'],
                'open': float(item['open']),
                'high': float(item['high']),
                'low': float(item['low']),
                'close': float(item['close']),
                'volume': float(item.get('volume', 0))
            })
        
        data.sort(key=lambda x: x['timestamp'])
        
        # Market Structure Analysis
        market_structure = self._analyze_market_structure(data)
        
        # Fair Value Gaps
        fair_value_gaps = self._identify_fair_value_gaps(data)
        
        # Liquidity Pools
        liquidity_pools = self._identify_liquidity_pools(data)
        
        # Premium/Discount Zones
        premium_discount = self._analyze_premium_discount(data, market_structure)
        
        # Trading Signal Generation
        signal = self._generate_trading_signal(data, market_structure, fair_value_gaps, 
                                             liquidity_pools, premium_discount)
        
        return {
            'market_structure': market_structure,
            'fair_value_gaps': fair_value_gaps,
            'liquidity_pools': liquidity_pools,
            'premium_discount': premium_discount,
            'trading_signal': signal,
            'analysis_timestamp': datetime.utcnow().isoformat()
        }
    
    def _analyze_market_structure(self, data: List[Dict]) -> Dict:
        """
        Analysiert die Marktstruktur basierend auf Higher Highs/Lows und Lower Highs/Lows
        """
        highs = self._find_swing_highs(data)
        lows = self._find_swing_lows(data)
        
        higher_highs = []
        higher_lows = []
        lower_highs = []
        lower_lows = []
        
        # Analysiere Swing-Punkte für Trend-Bestimmung
        for i in range(1, len(highs)):
            if highs[i]['price'] > highs[i-1]['price']:
                higher_highs.append(highs[i])
            else:
                lower_highs.append(highs[i])
                
        for i in range(1, len(lows)):
            if lows[i]['price'] > lows[i-1]['price']:
                higher_lows.append(lows[i])
            else:
                lower_lows.append(lows[i])
        
        # Trend-Bestimmung
        if len(higher_highs) >= 2 and len(higher_lows) >= 2:
            trend = 'bullish'
        elif len(lower_highs) >= 2 and len(lower_lows) >= 2:
            trend = 'bearish'
        else:
            trend = 'sideways'
            
        # Key Support/Resistance Levels
        key_levels = self._identify_key_levels(data, highs, lows)
        
        return {
            'trend': trend,
            'higher_highs': higher_highs,
            'higher_lows': higher_lows,
            'lower_highs': lower_highs,
            'lower_lows': lower_lows,
            'key_levels': key_levels,
            'current_price': float(data[-1]['close']),
            'strength': self._calculate_trend_strength(higher_highs, higher_lows, lower_highs, lower_lows)
        }
    
    def _find_swing_highs(self, data: List[Dict], window: int = 5) -> List[Dict]:
        """Findet Swing-Hochs in den Daten"""
        highs = []
        for i in range(window, len(data) - window):
            current_high = data[i]['high']
            is_swing_high = True
            
            # Prüfe ob aktueller Punkt höher als umgebende Punkte ist
            for j in range(i - window, i + window + 1):
                if j != i and data[j]['high'] >= current_high:
                    is_swing_high = False
                    break
                    
            if is_swing_high:
                highs.append({
                    'price': float(current_high),
                    'timestamp': data[i]['timestamp'].isoformat(),
                    'index': i
                })
                
        return highs
    
    def _find_swing_lows(self, data: List[Dict], window: int = 5) -> List[Dict]:
        """Findet Swing-Tiefs in den Daten"""
        lows = []
        for i in range(window, len(data) - window):
            current_low = data[i]['low']
            is_swing_low = True
            
            # Prüfe ob aktueller Punkt niedriger als umgebende Punkte ist
            for j in range(i - window, i + window + 1):
                if j != i and data[j]['low'] <= current_low:
                    is_swing_low = False
                    break
                    
            if is_swing_low:
                lows.append({
                    'price': float(current_low),
                    'timestamp': data[i]['timestamp'].isoformat(),
                    'index': i
                })
                
        return lows
    
    def _identify_fair_value_gaps(self, data: List[Dict]) -> List[Dict]:
        """
        Identifiziert Fair Value Gaps (Ungleichgewichte im Markt)
        """
        gaps = []
        
        for i in range(2, len(data)):
            # Bullish FVG: Low[i] > High[i-2]
            if data[i]['low'] > data[i-2]['high']:
                gap_size = data[i]['low'] - data[i-2]['high']
                if gap_size / data[i-1]['close'] > self.gap_threshold:
                    gaps.append({
                        'start_price': float(data[i-2]['high']),
                        'end_price': float(data[i]['low']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'gap_type': 'bullish',
                        'size': float(gap_size),
                        'filled': False
                    })
            
            # Bearish FVG: High[i] < Low[i-2]
            elif data[i]['high'] < data[i-2]['low']:
                gap_size = data[i-2]['low'] - data[i]['high']
                if gap_size / data[i-1]['close'] > self.gap_threshold:
                    gaps.append({
                        'start_price': float(data[i]['high']),
                        'end_price': float(data[i-2]['low']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'gap_type': 'bearish',
                        'size': float(gap_size),
                        'filled': False
                    })
        
        # Prüfe welche Gaps bereits gefüllt wurden
        current_price = data[-1]['close']
        for gap in gaps:
            if gap['gap_type'] == 'bullish':
                if current_price <= gap['start_price']:
                    gap['filled'] = True
            else:  # bearish
                if current_price >= gap['end_price']:
                    gap['filled'] = True
                    
        return gaps
    
    def _identify_liquidity_pools(self, data: List[Dict]) -> List[Dict]:
        """
        Identifiziert Liquiditätspools (Bereiche mit vielen Stop-Orders)
        """
        pools = []
        
        # Finde Bereiche mit hoher Volatilität (potenzielle Liquiditäts-Sweeps)
        for i in range(1, len(data)):
            price_change = abs(data[i]['close'] - data[i-1]['close'])
            avg_price = (data[i]['close'] + data[i-1]['close']) / 2
            
            if price_change / avg_price > self.liquidity_threshold:
                # Bestimme ob Buy-Stops oder Sell-Stops getroffen wurden
                if data[i]['close'] > data[i-1]['close']:
                    # Aufwärtsbewegung - Buy-Stops wurden getroffen
                    pools.append({
                        'price': float(data[i]['high']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'pool_type': 'buy_stops',
                        'strength': min(price_change / avg_price * 10, 1.0),
                        'volume': float(data[i].get('volume', 0))
                    })
                else:
                    # Abwärtsbewegung - Sell-Stops wurden getroffen
                    pools.append({
                        'price': float(data[i]['low']),
                        'timestamp': data[i]['timestamp'].isoformat(),
                        'pool_type': 'sell_stops',
                        'strength': min(price_change / avg_price * 10, 1.0),
                        'volume': float(data[i].get('volume', 0))
                    })
        
        return pools
    
    def _analyze_premium_discount(self, data: List[Dict], market_structure: Dict) -> Dict:
        """
        Bestimmt ob sich der Markt in Premium- oder Discount-Zone befindet
        """
        current_price = data[-1]['close']
        
        # Berechne Fibonacci-Levels basierend auf letztem Swing
        if market_structure['key_levels']:
            recent_high = max([level['price'] for level in market_structure['key_levels']])
            recent_low = min([level['price'] for level in market_structure['key_levels']])
            
            range_size = recent_high - recent_low
            
            # Fibonacci-Levels
            fib_levels = {
                '0.0': recent_low,
                '0.236': recent_low + range_size * 0.236,
                '0.382': recent_low + range_size * 0.382,
                '0.5': recent_low + range_size * 0.5,
                '0.618': recent_low + range_size * 0.618,
                '0.786': recent_low + range_size * 0.786,
                '1.0': recent_high
            }
            
            # Bestimme Zone
            if current_price > fib_levels['0.618']:
                zone = 'premium'
                risk_level = 'high'
            elif current_price < fib_levels['0.382']:
                zone = 'discount'
                risk_level = 'low'
            else:
                zone = 'neutral'
                risk_level = 'medium'
                
            return {
                'zone': zone,
                'risk_level': risk_level,
                'current_price': float(current_price),
                'fibonacci_levels': {k: float(v) for k, v in fib_levels.items()},
                'price_position': float((current_price - recent_low) / range_size)
            }
        
        return {
            'zone': 'neutral',
            'risk_level': 'medium',
            'current_price': float(current_price),
            'fibonacci_levels': {},
            'price_position': 0.5
        }
    
    def _generate_trading_signal(self, data: List[Dict], market_structure: Dict, 
                               fair_value_gaps: List[Dict], liquidity_pools: List[Dict],
                               premium_discount: Dict) -> Optional[Dict]:
        """
        Generiert Trading-Signal basierend auf ICT-Analyse
        """
        current_price = data[-1]['close']
        
        # Signal-Stärke berechnen
        signal_strength = 0.0
        reasoning = []
        
        # Market Structure Bewertung
        if market_structure['trend'] == 'bullish':
            signal_strength += 0.3
            reasoning.append("Bullische Marktstruktur erkannt")
        elif market_structure['trend'] == 'bearish':
            signal_strength -= 0.3
            reasoning.append("Bärische Marktstruktur erkannt")
        
        # Fair Value Gaps Bewertung
        unfilled_gaps = [gap for gap in fair_value_gaps if not gap['filled']]
        if unfilled_gaps:
            latest_gap = unfilled_gaps[-1]
            if latest_gap['gap_type'] == 'bullish' and current_price >= latest_gap['start_price']:
                signal_strength += 0.25
                reasoning.append(f"Bullish FVG bei {latest_gap['start_price']:.2f}")
            elif latest_gap['gap_type'] == 'bearish' and current_price <= latest_gap['end_price']:
                signal_strength -= 0.25
                reasoning.append(f"Bearish FVG bei {latest_gap['end_price']:.2f}")
        
        # Premium/Discount Bewertung
        if premium_discount['zone'] == 'discount':
            signal_strength += 0.2
            reasoning.append("Preis in Discount-Zone")
        elif premium_discount['zone'] == 'premium':
            signal_strength -= 0.2
            reasoning.append("Preis in Premium-Zone")
        
        # Liquiditätspools Bewertung
        recent_pools = liquidity_pools[-5:] if len(liquidity_pools) >= 5 else liquidity_pools
        if recent_pools:
            latest_pool = recent_pools[-1]
            if latest_pool['pool_type'] == 'sell_stops':
                signal_strength += 0.15
                reasoning.append("Sell-Stops wurden kürzlich getroffen")
            else:
                signal_strength -= 0.15
                reasoning.append("Buy-Stops wurden kürzlich getroffen")
        
        # Signal generieren wenn Stärke ausreichend
        if abs(signal_strength) > 0.4:  # Mindest-Konfidenz
            signal_type = 'BUY' if signal_strength > 0 else 'SELL'
            
            # Entry, SL und TP berechnen
            entry_price = float(current_price)
            
            if signal_type == 'BUY':
                # Für Buy: SL unter letztem Swing-Low, TP 1:1 RR
                recent_lows = [level['price'] for level in market_structure['key_levels'] 
                              if level['price'] < current_price]
                if recent_lows:
                    stop_loss = min(recent_lows[-2:]) if len(recent_lows) >= 2 else recent_lows[-1]
                else:
                    stop_loss = current_price * 0.999  # 0.1% SL als Fallback
                    
                risk_distance = entry_price - stop_loss
                take_profit = entry_price + risk_distance  # 1:1 RR
                
            else:  # SELL
                # Für Sell: SL über letztem Swing-High, TP 1:1 RR
                recent_highs = [level['price'] for level in market_structure['key_levels'] 
                               if level['price'] > current_price]
                if recent_highs:
                    stop_loss = max(recent_highs[-2:]) if len(recent_highs) >= 2 else recent_highs[-1]
                else:
                    stop_loss = current_price * 1.001  # 0.1% SL als Fallback
                    
                risk_distance = stop_loss - entry_price
                take_profit = entry_price - risk_distance  # 1:1 RR
            
            return {
                'signal_type': signal_type,
                'confidence': abs(signal_strength),
                'entry_price': entry_price,
                'stop_loss': float(stop_loss),
                'take_profit': float(take_profit),
                'reasoning': '; '.join(reasoning),
                'risk_reward_ratio': 1.0
            }
        
        return None
    
    def _identify_key_levels(self, data: List[Dict], highs: List[Dict], lows: List[Dict]) -> List[Dict]:
        """Identifiziert wichtige Support/Resistance Levels"""
        levels = []
        
        # Füge Swing-Highs als Resistance hinzu
        for high in highs[-5:]:  # Letzte 5 Highs
            levels.append({
                'price': high['price'],
                'timestamp': high['timestamp'],
                'level_type': 'resistance',
                'strength': 0.8
            })
        
        # Füge Swing-Lows als Support hinzu
        for low in lows[-5:]:  # Letzte 5 Lows
            levels.append({
                'price': low['price'],
                'timestamp': low['timestamp'],
                'level_type': 'support',
                'strength': 0.8
            })
        
        return levels
    
    def _calculate_trend_strength(self, hh: List, hl: List, lh: List, ll: List) -> float:
        """Berechnet die Stärke des aktuellen Trends"""
        bullish_signals = len(hh) + len(hl)
        bearish_signals = len(lh) + len(ll)
        total_signals = bullish_signals + bearish_signals
        
        if total_signals == 0:
            return 0.5
        
        return bullish_signals / total_signals
    
    def _empty_analysis(self) -> Dict:
        """Gibt leere Analyse zurück wenn nicht genug Daten vorhanden"""
        return {
            'market_structure': {'trend': 'sideways', 'strength': 0.5},
            'fair_value_gaps': [],
            'liquidity_pools': [],
            'premium_discount': {'zone': 'neutral', 'risk_level': 'medium'},
            'trading_signal': None,
            'analysis_timestamp': datetime.utcnow().isoformat()
        }

    def calculate_lot_size(self, account_balance: float, risk_percentage: float, 
                          entry_price: float, stop_loss: float, 
                          symbol: str = 'XAUUSD') -> float:
        """
        Berechnet die optimale Lotgröße basierend auf Risikomanagement
        """
        risk_amount = account_balance * (risk_percentage / 100)
        
        # Pip-Wert für XAUUSD (Gold)
        if symbol == 'XAUUSD':
            pip_value = 0.01  # 1 Pip = $0.01 für 0.01 Lot
            pip_size = 0.01   # 1 Pip = 0.01 für Gold
        else:
            pip_value = 0.0001
            pip_size = 0.0001
        
        # Berechne Risiko in Pips
        risk_in_price = abs(entry_price - stop_loss)
        risk_in_pips = risk_in_price / pip_size
        
        # Berechne Lotgröße
        if risk_in_pips > 0:
            lot_size = risk_amount / (risk_in_pips * pip_value * 100)  # 100 für Standard-Lot
            return round(lot_size, 2)
        
        return 0.01  # Minimum Lot Size

