import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import deque
import json
import statistics

class PerformanceMonitor:
    """
    Performance-Monitor für die Trading-App
    Überwacht System-Performance und App-Metriken
    """
    
    def __init__(self):
        self.metrics = {
            'response_times': deque(maxlen=1000),
            'api_calls': deque(maxlen=1000),
            'errors': deque(maxlen=100),
            'system_metrics': deque(maxlen=100)
        }
        
        self.start_time = datetime.utcnow()
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Performance-Schwellenwerte
        self.thresholds = {
            'response_time_warning': 1.0,  # Sekunden
            'response_time_critical': 3.0,
            'cpu_warning': 70.0,  # Prozent
            'cpu_critical': 90.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'error_rate_warning': 5.0,  # Prozent
            'error_rate_critical': 10.0
        }
        
        self.alerts = []
    
    def start_monitoring(self):
        """
        Startet kontinuierliches Performance-Monitoring
        """
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """
        Stoppt Performance-Monitoring
        """
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_loop(self):
        """
        Haupt-Monitoring-Schleife
        """
        while self.monitoring_active:
            try:
                # Sammle System-Metriken
                system_metrics = self._collect_system_metrics()
                self.metrics['system_metrics'].append(system_metrics)
                
                # Prüfe Schwellenwerte
                self._check_thresholds(system_metrics)
                
                # Bereinige alte Daten
                self._cleanup_old_metrics()
                
                time.sleep(30)  # Alle 30 Sekunden
                
            except Exception as e:
                self._log_error("monitoring_loop", str(e))
    
    def _collect_system_metrics(self) -> Dict:
        """
        Sammelt System-Performance-Metriken
        """
        try:
            # CPU-Nutzung
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Speicher-Nutzung
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available / (1024**3)  # GB
            
            # Disk-Nutzung
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free / (1024**3)  # GB
            
            # Netzwerk-Statistiken
            network = psutil.net_io_counters()
            
            # Prozess-spezifische Metriken
            process = psutil.Process()
            process_memory = process.memory_info().rss / (1024**2)  # MB
            process_cpu = process.cpu_percent()
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': round(memory_available, 2),
                'disk_percent': disk_percent,
                'disk_free_gb': round(disk_free, 2),
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
                'process_memory_mb': round(process_memory, 2),
                'process_cpu_percent': process_cpu
            }
            
        except Exception as e:
            self._log_error("collect_system_metrics", str(e))
            return {'timestamp': datetime.utcnow().isoformat(), 'error': str(e)}
    
    def track_api_call(self, endpoint: str, duration: float, status_code: int = 200):
        """
        Verfolgt API-Aufrufe und deren Performance
        """
        call_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'endpoint': endpoint,
            'duration': duration,
            'status_code': status_code
        }
        
        self.metrics['api_calls'].append(call_data)
        self.metrics['response_times'].append(duration)
        
        # Prüfe Response-Time-Schwellenwerte
        if duration > self.thresholds['response_time_critical']:
            self._create_alert('critical', f"Kritische Response-Zeit: {duration:.2f}s für {endpoint}")
        elif duration > self.thresholds['response_time_warning']:
            self._create_alert('warning', f"Langsame Response-Zeit: {duration:.2f}s für {endpoint}")
    
    def track_error(self, error_type: str, error_message: str, endpoint: str = None):
        """
        Verfolgt Fehler und Exceptions
        """
        error_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'error_type': error_type,
            'error_message': error_message,
            'endpoint': endpoint
        }
        
        self.metrics['errors'].append(error_data)
        
        # Prüfe Fehlerrate
        self._check_error_rate()
    
    def _check_thresholds(self, system_metrics: Dict):
        """
        Prüft System-Metriken gegen Schwellenwerte
        """
        if 'error' in system_metrics:
            return
        
        # CPU-Schwellenwerte
        cpu = system_metrics.get('cpu_percent', 0)
        if cpu > self.thresholds['cpu_critical']:
            self._create_alert('critical', f"Kritische CPU-Nutzung: {cpu:.1f}%")
        elif cpu > self.thresholds['cpu_warning']:
            self._create_alert('warning', f"Hohe CPU-Nutzung: {cpu:.1f}%")
        
        # Speicher-Schwellenwerte
        memory = system_metrics.get('memory_percent', 0)
        if memory > self.thresholds['memory_critical']:
            self._create_alert('critical', f"Kritische Speicher-Nutzung: {memory:.1f}%")
        elif memory > self.thresholds['memory_warning']:
            self._create_alert('warning', f"Hohe Speicher-Nutzung: {memory:.1f}%")
    
    def _check_error_rate(self):
        """
        Prüft aktuelle Fehlerrate
        """
        if len(self.metrics['api_calls']) < 10:
            return
        
        # Berechne Fehlerrate der letzten 100 API-Calls
        recent_calls = list(self.metrics['api_calls'])[-100:]
        error_calls = [call for call in recent_calls if call['status_code'] >= 400]
        
        if recent_calls:
            error_rate = (len(error_calls) / len(recent_calls)) * 100
            
            if error_rate > self.thresholds['error_rate_critical']:
                self._create_alert('critical', f"Kritische Fehlerrate: {error_rate:.1f}%")
            elif error_rate > self.thresholds['error_rate_warning']:
                self._create_alert('warning', f"Hohe Fehlerrate: {error_rate:.1f}%")
    
    def _create_alert(self, severity: str, message: str):
        """
        Erstellt Performance-Alert
        """
        alert = {
            'timestamp': datetime.utcnow().isoformat(),
            'severity': severity,
            'message': message
        }
        
        self.alerts.append(alert)
        
        # Behalte nur die letzten 50 Alerts
        if len(self.alerts) > 50:
            self.alerts = self.alerts[-50:]
        
        print(f"PERFORMANCE ALERT [{severity.upper()}]: {message}")
    
    def _cleanup_old_metrics(self):
        """
        Bereinigt alte Metriken (älter als 1 Stunde)
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        
        for metric_type in ['api_calls', 'errors']:
            if metric_type in self.metrics:
                self.metrics[metric_type] = deque([
                    item for item in self.metrics[metric_type]
                    if datetime.fromisoformat(item['timestamp']) > cutoff_time
                ], maxlen=self.metrics[metric_type].maxlen)
    
    def _log_error(self, context: str, error_message: str):
        """
        Loggt interne Monitoring-Fehler
        """
        print(f"Performance Monitor Error [{context}]: {error_message}")
    
    def get_performance_summary(self) -> Dict:
        """
        Gibt Performance-Zusammenfassung zurück
        """
        summary = {
            'uptime': str(datetime.utcnow() - self.start_time),
            'total_api_calls': len(self.metrics['api_calls']),
            'total_errors': len(self.metrics['errors']),
            'active_alerts': len([a for a in self.alerts if self._is_recent_alert(a)]),
            'response_time_stats': self._get_response_time_stats(),
            'current_system_metrics': self._get_latest_system_metrics(),
            'error_rate': self._calculate_current_error_rate(),
            'top_slow_endpoints': self._get_slow_endpoints()
        }
        
        return summary
    
    def _is_recent_alert(self, alert: Dict) -> bool:
        """
        Prüft ob Alert aus den letzten 10 Minuten ist
        """
        alert_time = datetime.fromisoformat(alert['timestamp'])
        return datetime.utcnow() - alert_time < timedelta(minutes=10)
    
    def _get_response_time_stats(self) -> Dict:
        """
        Berechnet Response-Time-Statistiken
        """
        if not self.metrics['response_times']:
            return {'avg': 0, 'min': 0, 'max': 0, 'p95': 0}
        
        times = list(self.metrics['response_times'])
        
        return {
            'avg': round(statistics.mean(times), 3),
            'min': round(min(times), 3),
            'max': round(max(times), 3),
            'p95': round(statistics.quantiles(times, n=20)[18], 3) if len(times) > 20 else round(max(times), 3)
        }
    
    def _get_latest_system_metrics(self) -> Dict:
        """
        Gibt neueste System-Metriken zurück
        """
        if not self.metrics['system_metrics']:
            return {}
        
        latest = self.metrics['system_metrics'][-1]
        if 'error' in latest:
            return {'status': 'error'}
        
        return {
            'cpu_percent': latest.get('cpu_percent', 0),
            'memory_percent': latest.get('memory_percent', 0),
            'disk_percent': latest.get('disk_percent', 0),
            'process_memory_mb': latest.get('process_memory_mb', 0)
        }
    
    def _calculate_current_error_rate(self) -> float:
        """
        Berechnet aktuelle Fehlerrate
        """
        if len(self.metrics['api_calls']) < 10:
            return 0.0
        
        recent_calls = list(self.metrics['api_calls'])[-100:]
        error_calls = [call for call in recent_calls if call['status_code'] >= 400]
        
        return round((len(error_calls) / len(recent_calls)) * 100, 2) if recent_calls else 0.0
    
    def _get_slow_endpoints(self) -> List[Dict]:
        """
        Identifiziert langsamste Endpoints
        """
        if not self.metrics['api_calls']:
            return []
        
        # Gruppiere nach Endpoint
        endpoint_times = {}
        for call in self.metrics['api_calls']:
            endpoint = call['endpoint']
            if endpoint not in endpoint_times:
                endpoint_times[endpoint] = []
            endpoint_times[endpoint].append(call['duration'])
        
        # Berechne Durchschnittszeiten
        slow_endpoints = []
        for endpoint, times in endpoint_times.items():
            avg_time = statistics.mean(times)
            slow_endpoints.append({
                'endpoint': endpoint,
                'avg_response_time': round(avg_time, 3),
                'call_count': len(times)
            })
        
        # Sortiere nach langsamsten
        slow_endpoints.sort(key=lambda x: x['avg_response_time'], reverse=True)
        return slow_endpoints[:5]
    
    def get_health_status(self) -> Dict:
        """
        Gibt Gesundheitsstatus der App zurück
        """
        recent_alerts = [a for a in self.alerts if self._is_recent_alert(a)]
        critical_alerts = [a for a in recent_alerts if a['severity'] == 'critical']
        
        # Bestimme Gesundheitsstatus
        if critical_alerts:
            status = 'critical'
            message = f"{len(critical_alerts)} kritische Probleme erkannt"
        elif len(recent_alerts) > 5:
            status = 'warning'
            message = f"{len(recent_alerts)} Warnungen in den letzten 10 Minuten"
        else:
            status = 'healthy'
            message = "Alle Systeme funktionieren normal"
        
        return {
            'status': status,
            'message': message,
            'uptime': str(datetime.utcnow() - self.start_time),
            'recent_alerts': len(recent_alerts),
            'critical_alerts': len(critical_alerts),
            'last_check': datetime.utcnow().isoformat()
        }
    
    def export_metrics(self, format: str = 'json') -> str:
        """
        Exportiert Metriken in verschiedenen Formaten
        """
        data = {
            'summary': self.get_performance_summary(),
            'health': self.get_health_status(),
            'recent_alerts': [a for a in self.alerts if self._is_recent_alert(a)],
            'export_timestamp': datetime.utcnow().isoformat()
        }
        
        if format == 'json':
            return json.dumps(data, indent=2)
        else:
            return str(data)

# Decorator für automatisches Performance-Tracking
def track_performance(monitor: PerformanceMonitor, endpoint_name: str = None):
    """
    Decorator für automatisches Performance-Tracking von Funktionen
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            endpoint = endpoint_name or func.__name__
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                monitor.track_api_call(endpoint, duration, 200)
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                monitor.track_api_call(endpoint, duration, 500)
                monitor.track_error(type(e).__name__, str(e), endpoint)
                raise
                
        return wrapper
    return decorator

# Globale Instanz
performance_monitor = PerformanceMonitor()

