import requests
import json
import time
from typing import Dict, Optional
from datetime import datetime
import hashlib
import hmac
import base64

class VantageTrader:
    def __init__(self):
        # Vantage Markets API Konfiguration
        # WICHTIG: Diese müssen durch echte Credentials ersetzt werden
        self.api_key = "DEMO_API_KEY"  # Ersetzen mit echtem API Key
        self.api_secret = "DEMO_API_SECRET"  # Ersetzen mit echtem API Secret
        self.account_id = "DEMO_ACCOUNT"  # Ersetzen mit echter Account ID
        
        # Vantage Markets API Endpoints
        self.base_url = "https://api.vantage.com/v1"  # Beispiel-URL
        self.demo_mode = True  # Für Demo-Zwecke
        
        # Trading-Parameter
        self.symbol_mapping = {
            'XAUUSD': 'GOLD',  # Vantage Symbol für Gold
            'EURUSD': 'EURUSD',
            'GBPUSD': 'GBPUSD'
        }
    
    def _generate_signature(self, method: str, endpoint: str, params: Dict) -> str:
        """
        Generiert Signatur für Vantage API-Authentifizierung
        """
        if self.demo_mode:
            return "demo_signature"
        
        # Sortiere Parameter
        sorted_params = sorted(params.items())
        query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
        
        # Erstelle String zum Signieren
        string_to_sign = f"{method}\n{endpoint}\n{query_string}"
        
        # Generiere HMAC-SHA256 Signatur
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        return base64.b64encode(signature).decode('utf-8')
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None) -> Dict:
        """
        Macht authentifizierten Request zur Vantage API
        """
        if self.demo_mode:
            return self._simulate_api_response(method, endpoint, params)
        
        if params is None:
            params = {}
        
        # Füge Standard-Parameter hinzu
        params.update({
            'api_key': self.api_key,
            'timestamp': int(time.time() * 1000),
            'account_id': self.account_id
        })
        
        # Generiere Signatur
        signature = self._generate_signature(method, endpoint, params)
        params['signature'] = signature
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url, params=params, timeout=30)
            elif method == 'POST':
                response = requests.post(url, json=params, timeout=30)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f"API Request failed: {str(e)}"
            }
    
    def _simulate_api_response(self, method: str, endpoint: str, params: Dict) -> Dict:
        """
        Simuliert Vantage API-Antworten für Demo-Zwecke
        """
        if endpoint == '/orders' and method == 'POST':
            # Simuliere Order-Platzierung
            order_id = f"DEMO_{int(time.time())}"
            
            return {
                'success': True,
                'order_id': order_id,
                'status': 'FILLED',
                'symbol': params.get('symbol', 'XAUUSD'),
                'side': params.get('side', 'BUY'),
                'quantity': params.get('quantity', 0.01),
                'price': params.get('price', 2000.0),
                'stop_loss': params.get('stop_loss'),
                'take_profit': params.get('take_profit'),
                'timestamp': datetime.utcnow().isoformat(),
                'commission': 0.50,
                'message': 'Demo order successfully placed'
            }
        
        elif endpoint == '/account' and method == 'GET':
            # Simuliere Account-Info
            return {
                'success': True,
                'account_id': self.account_id,
                'balance': 100.0,
                'equity': 100.0,
                'margin': 0.0,
                'free_margin': 100.0,
                'currency': 'USD',
                'leverage': 100
            }
        
        elif endpoint == '/positions' and method == 'GET':
            # Simuliere offene Positionen
            return {
                'success': True,
                'positions': []
            }
        
        elif endpoint.startswith('/orders/') and method == 'DELETE':
            # Simuliere Order-Schließung
            return {
                'success': True,
                'message': 'Order successfully closed',
                'order_id': endpoint.split('/')[-1]
            }
        
        else:
            return {
                'success': False,
                'error': f"Unknown endpoint: {endpoint}"
            }
    
    def place_order(self, symbol: str, order_type: str, lot_size: float,
                   entry_price: float, stop_loss: float, take_profit: float) -> Dict:
        """
        Platziert Order bei Vantage Markets
        """
        try:
            # Mappe Symbol
            vantage_symbol = self.symbol_mapping.get(symbol, symbol)
            
            # Bereite Order-Parameter vor
            order_params = {
                'symbol': vantage_symbol,
                'side': order_type.upper(),  # BUY oder SELL
                'type': 'MARKET',  # Market Order für sofortige Ausführung
                'quantity': lot_size,
                'price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'time_in_force': 'GTC'  # Good Till Cancelled
            }
            
            # Sende Order
            response = self._make_request('POST', '/orders', order_params)
            
            if response.get('success'):
                return {
                    'success': True,
                    'order_id': response.get('order_id'),
                    'status': response.get('status'),
                    'message': response.get('message', 'Order successfully placed'),
                    'details': response
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Unknown error'),
                    'details': response
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Order placement failed: {str(e)}"
            }
    
    def close_position(self, order_id: str) -> Dict:
        """
        Schließt offene Position
        """
        try:
            response = self._make_request('DELETE', f'/orders/{order_id}')
            
            if response.get('success'):
                return {
                    'success': True,
                    'message': 'Position successfully closed',
                    'order_id': order_id
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Failed to close position')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Position closure failed: {str(e)}"
            }
    
    def get_account_info(self) -> Dict:
        """
        Holt Account-Informationen
        """
        try:
            response = self._make_request('GET', '/account')
            
            if response.get('success'):
                return {
                    'success': True,
                    'account': response
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Failed to get account info')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Account info request failed: {str(e)}"
            }
    
    def get_open_positions(self) -> Dict:
        """
        Holt offene Positionen
        """
        try:
            response = self._make_request('GET', '/positions')
            
            if response.get('success'):
                return {
                    'success': True,
                    'positions': response.get('positions', [])
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Failed to get positions')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Positions request failed: {str(e)}"
            }
    
    def modify_order(self, order_id: str, stop_loss: Optional[float] = None,
                    take_profit: Optional[float] = None) -> Dict:
        """
        Modifiziert bestehende Order
        """
        try:
            params = {'order_id': order_id}
            
            if stop_loss is not None:
                params['stop_loss'] = stop_loss
            if take_profit is not None:
                params['take_profit'] = take_profit
            
            response = self._make_request('PUT', f'/orders/{order_id}', params)
            
            if response.get('success'):
                return {
                    'success': True,
                    'message': 'Order successfully modified',
                    'order_id': order_id
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Failed to modify order')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Order modification failed: {str(e)}"
            }
    
    def get_trading_history(self, days: int = 7) -> Dict:
        """
        Holt Trading-Historie
        """
        try:
            params = {
                'days': days,
                'limit': 100
            }
            
            response = self._make_request('GET', '/history', params)
            
            if response.get('success'):
                return {
                    'success': True,
                    'history': response.get('trades', [])
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', 'Failed to get trading history')
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"History request failed: {str(e)}"
            }
    
    def set_demo_mode(self, demo: bool):
        """
        Aktiviert/Deaktiviert Demo-Modus
        """
        self.demo_mode = demo
    
    def configure_credentials(self, api_key: str, api_secret: str, account_id: str):
        """
        Konfiguriert echte Vantage-Credentials
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.account_id = account_id
        self.demo_mode = False

# Singleton-Instanz für die App
vantage_trader = VantageTrader()

