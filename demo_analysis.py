#!/usr/bin/env python3
"""
Demo-Analyse für Gold-USD Trading basierend auf ICT-Konzepten
Simuliert eine Live-Analyse mit den aktuellen Marktdaten
"""

import sys
import os
sys.path.append('/home/<USER>/gold_trading_app')

from src.ict_analyzer import ICTAnalyzer
from src.market_data import MarketDataProvider
import json

def main():
    print("🥇 Gold Trading App - ICT Demo-Analyse")
    print("=" * 50)
    
    # Initialisiere Komponenten
    analyzer = ICTAnalyzer()
    market_provider = MarketDataProvider()
    
    # Trading-Parameter (wie vom Benutzer angegeben)
    ACCOUNT_BALANCE = 100.0  # $100
    RISK_PERCENTAGE = 10.0   # 10%
    RISK_REWARD_RATIO = 1.0  # 1:1
    
    print(f"💰 Kontostand: ${ACCOUNT_BALANCE}")
    print(f"⚠️  Risiko: {RISK_PERCENTAGE}%")
    print(f"📊 Risk-Reward: 1:{RISK_REWARD_RATIO}")
    print()
    
    # Hole aktuelle Marktdaten
    print("📈 Lade aktuelle Gold-USD Marktdaten...")
    market_data = market_provider.get_gold_data(interval='1min', limit=100)
    
    if not market_data:
        print("❌ Keine Marktdaten verfügbar")
        return
    
    current_price = market_data[-1]['close']
    print(f"💎 Aktueller Gold-Preis: ${current_price:.2f}")
    print()
    
    # Führe ICT-Analyse durch
    print("🧠 Führe ICT-Analyse durch...")
    analysis = analyzer.analyze_market_data(market_data)
    
    # Zeige Analyseergebnisse
    print("\n📊 ICT-ANALYSE ERGEBNISSE:")
    print("-" * 30)
    
    # Market Structure
    ms = analysis['market_structure']
    print(f"📈 Marktstruktur:")
    print(f"   Trend: {ms['trend'].upper()}")
    print(f"   Stärke: {ms['strength']*100:.1f}%")
    print(f"   Aktueller Preis: ${ms['current_price']:.2f}")
    
    # Premium/Discount
    pd = analysis['premium_discount']
    print(f"\n💰 Premium/Discount:")
    print(f"   Zone: {pd['zone'].upper()}")
    print(f"   Risiko-Level: {pd['risk_level'].upper()}")
    print(f"   Position: {pd['price_position']*100:.1f}%")
    
    # Fair Value Gaps
    fvgs = analysis['fair_value_gaps']
    unfilled_fvgs = [gap for gap in fvgs if not gap['filled']]
    print(f"\n⚡ Fair Value Gaps:")
    print(f"   Gesamt: {len(fvgs)}")
    print(f"   Ungefüllt: {len(unfilled_fvgs)}")
    if unfilled_fvgs:
        latest_gap = unfilled_fvgs[-1]
        print(f"   Letztes: {latest_gap['gap_type']} @ ${latest_gap['start_price']:.2f}")
    
    # Liquidity Pools
    lps = analysis['liquidity_pools']
    print(f"\n🌊 Liquiditätspools:")
    print(f"   Erkannt: {len(lps)}")
    if lps:
        latest_pool = lps[-1]
        print(f"   Letzter: {latest_pool['pool_type']} @ ${latest_pool['price']:.2f}")
    
    # Trading Signal
    signal = analysis['trading_signal']
    print(f"\n🎯 TRADING-SIGNAL:")
    print("-" * 20)
    
    if signal:
        print(f"✅ Signal erkannt!")
        print(f"   Typ: {signal['signal_type']}")
        print(f"   Konfidenz: {signal['confidence']*100:.1f}%")
        print(f"   Entry: ${signal['entry_price']:.2f}")
        print(f"   Stop Loss: ${signal['stop_loss']:.2f}")
        print(f"   Take Profit: ${signal['take_profit']:.2f}")
        print(f"   Begründung: {signal['reasoning']}")
        
        # Berechne Lotgröße
        lot_size = analyzer.calculate_lot_size(
            account_balance=ACCOUNT_BALANCE,
            risk_percentage=RISK_PERCENTAGE,
            entry_price=signal['entry_price'],
            stop_loss=signal['stop_loss'],
            symbol='XAUUSD'
        )
        
        risk_amount = ACCOUNT_BALANCE * (RISK_PERCENTAGE / 100)
        
        print(f"\n💼 TRADE-DETAILS:")
        print(f"   Lotgröße: {lot_size}")
        print(f"   Risiko-Betrag: ${risk_amount:.2f}")
        
        # Finale Empfehlung
        print(f"\n🎯 EMPFEHLUNG:")
        print(f"Ich empfehle einen {signal['signal_type']} mit einem TP bei ${signal['take_profit']:.2f} und einem SL bei ${signal['stop_loss']:.2f}.")
        print(f"Lotgröße: {lot_size}")
        print(f"\nKurzanalyse: {signal['reasoning']}")
        
    else:
        print("❌ Kein Trading-Signal generiert")
        print("   Marktbedingungen erfüllen nicht die ICT-Kriterien für einen optimalen Trade.")
        
        # Erstelle manuelle Empfehlung basierend auf aktueller Analyse
        print(f"\n📝 MANUELLE ANALYSE:")
        
        # Bestimme Trend-Richtung
        if ms['trend'] == 'bullish' and pd['zone'] == 'discount':
            direction = "BUY"
            entry = current_price
            # SL unter letztem Support
            stop_loss = current_price * 0.999  # 0.1% SL
            take_profit = entry + (entry - stop_loss)  # 1:1 RR
            reasoning = f"Bullische Marktstruktur ({ms['strength']*100:.1f}% Stärke) in Discount-Zone ({pd['zone']})"
            
        elif ms['trend'] == 'bearish' and pd['zone'] == 'premium':
            direction = "SELL"
            entry = current_price
            # SL über letztem Resistance
            stop_loss = current_price * 1.001  # 0.1% SL
            take_profit = entry - (stop_loss - entry)  # 1:1 RR
            reasoning = f"Bärische Marktstruktur in Premium-Zone ({pd['zone']})"
            
        else:
            # Neutral/Vorsichtige Empfehlung
            if ms['trend'] == 'bullish':
                direction = "BUY"
                entry = current_price
                stop_loss = current_price * 0.9995  # 0.05% SL (konservativ)
                take_profit = entry + (entry - stop_loss)
                reasoning = f"Schwache bullische Signale, konservativer Ansatz empfohlen"
            else:
                direction = "SELL"
                entry = current_price
                stop_loss = current_price * 1.0005  # 0.05% SL (konservativ)
                take_profit = entry - (stop_loss - entry)
                reasoning = f"Schwache bärische Signale, konservativer Ansatz empfohlen"
        
        # Berechne Lotgröße für manuelle Empfehlung
        lot_size = analyzer.calculate_lot_size(
            account_balance=ACCOUNT_BALANCE,
            risk_percentage=RISK_PERCENTAGE,
            entry_price=entry,
            stop_loss=stop_loss,
            symbol='XAUUSD'
        )
        
        print(f"\n🎯 EMPFEHLUNG:")
        print(f"Ich empfehle einen {direction} mit einem TP bei ${take_profit:.2f} und einem SL bei ${stop_loss:.2f}.")
        print(f"Lotgröße: {lot_size}")
        print(f"\nKurzanalyse: {reasoning}")
    
    print(f"\n" + "=" * 50)
    print("✅ Analyse abgeschlossen!")

if __name__ == "__main__":
    main()

