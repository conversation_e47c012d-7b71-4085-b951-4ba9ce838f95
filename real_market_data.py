import requests
import json
import time
from datetime import datetime, timedelta
import random
import math

class RealMarketDataProvider:
    """Echte Marktdaten-Provider für Gold (XAUUSD)"""
    
    def __init__(self):
        self.cache = {}
        self.cache_duration = 60  # 1 Minute Cache
        
        # API Keys (kostenlose Tiers)
        self.alpha_vantage_key = "demo"  # Ersetzen mit echtem Key
        self.twelve_data_key = "demo"    # Ersetzen mit echtem Key
        
        # Fallback für Demo-Zwecke
        self.base_price = 2000.0
        self.last_price = self.base_price
        
    def get_gold_price_yahoo(self):
        """Hole Gold-Preis von Yahoo Finance (kostenlos)"""
        try:
            # Yahoo Finance API für Gold
            url = "https://query1.finance.yahoo.com/v8/finance/chart/GC=F"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                    result = data['chart']['result'][0]
                    meta = result.get('meta', {})
                    
                    current_price = meta.get('regularMarketPrice', self.last_price)
                    previous_close = meta.get('previousClose', current_price)
                    
                    return {
                        'price': float(current_price),
                        'previous_close': float(previous_close),
                        'change': float(current_price - previous_close),
                        'change_percent': float((current_price - previous_close) / previous_close * 100),
                        'timestamp': datetime.utcnow().isoformat(),
                        'source': 'yahoo_finance'
                    }
        except Exception as e:
            print(f"Yahoo Finance API Fehler: {e}")
            
        return None
    
    def get_gold_price_alpha_vantage(self):
        """Hole Gold-Preis von Alpha Vantage"""
        try:
            url = f"https://www.alphavantage.co/query"
            params = {
                'function': 'GLOBAL_QUOTE',
                'symbol': 'XAUUSD',
                'apikey': self.alpha_vantage_key
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                if 'Global Quote' in data:
                    quote = data['Global Quote']
                    current_price = float(quote.get('05. price', self.last_price))
                    previous_close = float(quote.get('08. previous close', current_price))
                    
                    return {
                        'price': current_price,
                        'previous_close': previous_close,
                        'change': current_price - previous_close,
                        'change_percent': (current_price - previous_close) / previous_close * 100,
                        'timestamp': datetime.utcnow().isoformat(),
                        'source': 'alpha_vantage'
                    }
        except Exception as e:
            print(f"Alpha Vantage API Fehler: {e}")
            
        return None
    
    def get_gold_price_twelve_data(self):
        """Hole Gold-Preis von Twelve Data"""
        try:
            url = f"https://api.twelvedata.com/price"
            params = {
                'symbol': 'XAUUSD',
                'apikey': self.twelve_data_key
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                if 'price' in data:
                    current_price = float(data['price'])
                    
                    return {
                        'price': current_price,
                        'previous_close': self.last_price,
                        'change': current_price - self.last_price,
                        'change_percent': (current_price - self.last_price) / self.last_price * 100,
                        'timestamp': datetime.utcnow().isoformat(),
                        'source': 'twelve_data'
                    }
        except Exception as e:
            print(f"Twelve Data API Fehler: {e}")
            
        return None
    
    def get_realistic_simulation(self):
        """Realistische Gold-Preis-Simulation basierend auf Marktzeiten"""
        current_time = datetime.utcnow()
        hour = current_time.hour
        
        # Marktzeiten und Volatilität
        if 8 <= hour <= 17:  # London/NY Session
            volatility = 0.8
            trend_strength = 0.6
        elif 22 <= hour <= 23 or 0 <= hour <= 2:  # Asiatische Session
            volatility = 0.4
            trend_strength = 0.3
        else:  # Ruhige Zeiten
            volatility = 0.2
            trend_strength = 0.1
        
        # Realistische Preisbewegung
        random_walk = random.gauss(0, volatility)
        trend_component = math.sin(time.time() / 3600) * trend_strength
        
        price_change = random_walk + trend_component
        new_price = self.last_price + price_change
        
        # Preisbegrenzungen (Gold bewegt sich normalerweise zwischen $1800-$2200)
        new_price = max(1800, min(2200, new_price))
        
        change = new_price - self.last_price
        change_percent = (change / self.last_price) * 100
        
        self.last_price = new_price
        
        return {
            'price': round(new_price, 2),
            'previous_close': round(self.last_price - change, 2),
            'change': round(change, 2),
            'change_percent': round(change_percent, 4),
            'timestamp': current_time.isoformat(),
            'source': 'realistic_simulation',
            'market_session': self.get_market_session(hour),
            'volatility': volatility
        }
    
    def get_market_session(self, hour):
        """Bestimme aktuelle Marktsession"""
        if 8 <= hour <= 17:
            return "London/NY"
        elif 22 <= hour <= 23 or 0 <= hour <= 2:
            return "Asien"
        else:
            return "Ruhig"
    
    def get_current_price(self):
        """Hole aktuellen Gold-Preis mit Fallback-System"""
        cache_key = 'current_price'
        
        # Prüfe Cache
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
        
        # Versuche verschiedene APIs in Reihenfolge
        price_data = None
        
        # 1. Yahoo Finance (kostenlos, zuverlässig)
        price_data = self.get_gold_price_yahoo()
        
        # 2. Alpha Vantage (Fallback)
        if not price_data:
            price_data = self.get_gold_price_alpha_vantage()
        
        # 3. Twelve Data (Fallback)
        if not price_data:
            price_data = self.get_gold_price_twelve_data()
        
        # 4. Realistische Simulation (letzter Fallback)
        if not price_data:
            price_data = self.get_realistic_simulation()
        
        # Cache das Ergebnis
        if price_data:
            self.cache[cache_key] = (price_data, time.time())
            
        return price_data
    
    def get_historical_data(self, days=30):
        """Hole historische Daten für Charts"""
        cache_key = f'historical_{days}'
        
        # Prüfe Cache
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration * 10:  # 10 Minuten Cache
                return cached_data
        
        try:
            # Versuche Yahoo Finance für historische Daten
            end_time = int(time.time())
            start_time = end_time - (days * 24 * 60 * 60)
            
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/GC=F"
            params = {
                'period1': start_time,
                'period2': end_time,
                'interval': '1h',
                'includePrePost': 'true'
            }
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                    result = data['chart']['result'][0]
                    timestamps = result.get('timestamp', [])
                    indicators = result.get('indicators', {})
                    quote = indicators.get('quote', [{}])[0]
                    
                    opens = quote.get('open', [])
                    highs = quote.get('high', [])
                    lows = quote.get('low', [])
                    closes = quote.get('close', [])
                    volumes = quote.get('volume', [])
                    
                    historical_data = []
                    for i, timestamp in enumerate(timestamps):
                        if i < len(closes) and closes[i] is not None:
                            historical_data.append({
                                'timestamp': datetime.fromtimestamp(timestamp).isoformat(),
                                'open': round(opens[i] if i < len(opens) and opens[i] else closes[i], 2),
                                'high': round(highs[i] if i < len(highs) and highs[i] else closes[i], 2),
                                'low': round(lows[i] if i < len(lows) and lows[i] else closes[i], 2),
                                'close': round(closes[i], 2),
                                'volume': int(volumes[i] if i < len(volumes) and volumes[i] else 1000)
                            })
                    
                    if historical_data:
                        self.cache[cache_key] = (historical_data, time.time())
                        return historical_data
        
        except Exception as e:
            print(f"Historische Daten Fehler: {e}")
        
        # Fallback: Generiere realistische historische Daten
        return self.generate_realistic_historical_data(days)
    
    def generate_realistic_historical_data(self, days=30):
        """Generiere realistische historische Daten als Fallback"""
        data = []
        current_time = datetime.utcnow()
        current_price = self.get_current_price()['price']
        
        # Starte mit einem Preis vor 'days' Tagen
        start_price = current_price + random.gauss(0, 50)
        
        for i in range(days * 24):  # Stündliche Daten
            timestamp = current_time - timedelta(hours=days * 24 - i)
            hour = timestamp.hour
            
            # Marktzeiten-basierte Volatilität
            if 8 <= hour <= 17:
                volatility = 2.0
            elif 22 <= hour <= 23 or 0 <= hour <= 2:
                volatility = 1.0
            else:
                volatility = 0.5
            
            # Preisbewegung
            if i == 0:
                price = start_price
            else:
                price_change = random.gauss(0, volatility)
                price = max(1800, min(2200, data[-1]['close'] + price_change))
            
            # OHLC generieren
            high = price + random.uniform(0, volatility)
            low = price - random.uniform(0, volatility)
            close = price + random.gauss(0, volatility * 0.5)
            
            # Sicherstellen dass High >= Low
            high = max(high, low, close, price)
            low = min(low, close, price)
            
            data.append({
                'timestamp': timestamp.isoformat(),
                'open': round(price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': random.randint(500, 5000)
            })
        
        return data
    
    def get_market_status(self):
        """Hole aktuellen Marktstatus"""
        current_time = datetime.utcnow()
        hour = current_time.hour
        
        session = self.get_market_session(hour)
        
        if session == "London/NY":
            activity = "Hoch"
            is_open = True
        elif session == "Asien":
            activity = "Mittel"
            is_open = True
        else:
            activity = "Niedrig"
            is_open = False
        
        return {
            'session': session,
            'activity_level': activity,
            'market_open': is_open,
            'server_time': current_time.isoformat(),
            'next_session': self.get_next_session(hour)
        }
    
    def get_next_session(self, current_hour):
        """Bestimme nächste Marktsession"""
        if current_hour < 8:
            return f"London/NY in {8 - current_hour} Stunden"
        elif current_hour < 17:
            return f"Asien in {22 - current_hour} Stunden"
        elif current_hour < 22:
            return f"Asien in {22 - current_hour} Stunden"
        else:
            return f"London/NY in {24 - current_hour + 8} Stunden"

# Globale Instanz
real_market_data = RealMarketDataProvider()

