from src.models.user import db
from datetime import datetime
import json

class Trade(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(10), nullable=False, default='XAUUSD')
    direction = db.Column(db.String(4), nullable=False)  # BUY or SELL
    entry_price = db.Column(db.Float, nullable=False)
    stop_loss = db.Column(db.Float, nullable=False)
    take_profit = db.Column(db.Float, nullable=False)
    lot_size = db.Column(db.Float, nullable=False)
    risk_amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), default='PENDING')  # PENDING, ACTIVE, CLOSED
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    closed_at = db.Column(db.DateTime)
    profit_loss = db.Column(db.Float, default=0.0)
    
    # ICT Analysis Data
    market_structure = db.Column(db.Text)  # JSON string
    liquidity_pools = db.Column(db.Text)  # JSON string
    fair_value_gaps = db.Column(db.Text)  # JSON string
    premium_discount = db.Column(db.String(20))  # PREMIUM, DISCOUNT, NEUTRAL
    
    def to_dict(self):
        return {
            'id': self.id,
            'symbol': self.symbol,
            'direction': self.direction,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'lot_size': self.lot_size,
            'risk_amount': self.risk_amount,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'closed_at': self.closed_at.isoformat() if self.closed_at else None,
            'profit_loss': self.profit_loss,
            'market_structure': json.loads(self.market_structure) if self.market_structure else None,
            'liquidity_pools': json.loads(self.liquidity_pools) if self.liquidity_pools else None,
            'fair_value_gaps': json.loads(self.fair_value_gaps) if self.fair_value_gaps else None,
            'premium_discount': self.premium_discount
        }

class MarketData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(10), nullable=False, default='XAUUSD')
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    open_price = db.Column(db.Float, nullable=False)
    high_price = db.Column(db.Float, nullable=False)
    low_price = db.Column(db.Float, nullable=False)
    close_price = db.Column(db.Float, nullable=False)
    volume = db.Column(db.Float, default=0.0)
    
    def to_dict(self):
        return {
            'id': self.id,
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'open': self.open_price,
            'high': self.high_price,
            'low': self.low_price,
            'close': self.close_price,
            'volume': self.volume
        }

class TradingSignal(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    symbol = db.Column(db.String(10), nullable=False, default='XAUUSD')
    signal_type = db.Column(db.String(10), nullable=False)  # BUY, SELL
    confidence = db.Column(db.Float, nullable=False)  # 0.0 to 1.0
    entry_price = db.Column(db.Float, nullable=False)
    stop_loss = db.Column(db.Float, nullable=False)
    take_profit = db.Column(db.Float, nullable=False)
    reasoning = db.Column(db.Text)  # ICT analysis reasoning
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    executed = db.Column(db.Boolean, default=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'confidence': self.confidence,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'reasoning': self.reasoning,
            'created_at': self.created_at.isoformat(),
            'executed': self.executed
        }

