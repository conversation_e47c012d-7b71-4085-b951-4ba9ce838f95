import requests
import json
import time
from datetime import datetime, timedelta
import random
import math
from typing import Dict, List, Optional, Any
import threading
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedMarketDataProvider:
    """
    Verbesserte Marktdaten-Provider für Gold (XAUUSD) mit:
    - Mehreren zuverlässigen Datenquellen
    - Intelligenter Fallback-Logik
    - Verbesserter Caching-Strategie
    - Echtzeit-Datenvalidierung
    """
    
    def __init__(self):
        self.cache = {}
        self.cache_duration = 30  # 30 Sekunden Cache für Live-Daten
        self.historical_cache_duration = 300  # 5 Minuten für historische Daten
        
        # API Keys (können durch echte Keys ersetzt werden)
        self.alpha_vantage_key = "demo"
        self.twelve_data_key = "demo"
        
        # Fallback-Daten
        self.base_price = 2000.0
        self.last_known_price = self.base_price
        self.price_trend = 0.0  # Trend für realistische Simulation
        
        # Thread-Safety
        self.lock = threading.Lock()
        
        # Datenquellen-Status
        self.source_status = {
            'yahoo_finance': {'available': True, 'last_error': None, 'error_count': 0},
            'alpha_vantage': {'available': True, 'last_error': None, 'error_count': 0},
            'twelve_data': {'available': True, 'last_error': None, 'error_count': 0}
        }
        
    def _update_source_status(self, source: str, success: bool, error: str = None):
        """Update source availability status"""
        with self.lock:
            if success:
                self.source_status[source]['available'] = True
                self.source_status[source]['error_count'] = 0
                self.source_status[source]['last_error'] = None
            else:
                self.source_status[source]['error_count'] += 1
                self.source_status[source]['last_error'] = error
                # Disable source after 3 consecutive errors
                if self.source_status[source]['error_count'] >= 3:
                    self.source_status[source]['available'] = False
                    logger.warning(f"Disabled data source {source} after {self.source_status[source]['error_count']} errors")
    
    def _is_cache_valid(self, cache_key: str, duration: int = None) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.cache:
            return False
        
        cached_data, timestamp = self.cache[cache_key]
        cache_duration = duration or self.cache_duration
        return time.time() - timestamp < cache_duration
    
    def get_yahoo_finance_data(self) -> Optional[Dict[str, Any]]:
        """Hole Gold-Preis von Yahoo Finance mit verbesserter Fehlerbehandlung"""
        if not self.source_status['yahoo_finance']['available']:
            return None
            
        try:
            url = "https://query1.finance.yahoo.com/v8/finance/chart/XAUUSD=X"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            
            if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                result = data['chart']['result'][0]
                meta = result.get('meta', {})
                
                # Hole auch die neuesten Preisdaten
                indicators = result.get('indicators', {})
                quote = indicators.get('quote', [{}])[0] if indicators.get('quote') else {}
                
                current_price = meta.get('regularMarketPrice')
                if not current_price and quote.get('close'):
                    # Fallback zu letztem Close-Preis
                    closes = [c for c in quote.get('close', []) if c is not None]
                    if closes:
                        current_price = closes[-1]
                
                previous_close = meta.get('previousClose', current_price)
                
                if current_price:
                    self.last_known_price = float(current_price)
                    price_data = {
                        'price': float(current_price),
                        'previous_close': float(previous_close),
                        'change': float(current_price - previous_close),
                        'change_percent': float((current_price - previous_close) / previous_close * 100) if previous_close else 0.0,
                        'timestamp': datetime.utcnow().isoformat(),
                        'source': 'yahoo_finance',
                        'market_state': meta.get('marketState', 'UNKNOWN'),
                        'currency': meta.get('currency', 'USD'),
                        'volume': meta.get('regularMarketVolume', 0)
                    }
                    
                    self._update_source_status('yahoo_finance', True)
                    return price_data
                    
        except Exception as e:
            error_msg = f"Yahoo Finance API error: {str(e)}"
            logger.error(error_msg)
            self._update_source_status('yahoo_finance', False, error_msg)
            
        return None
    
    def get_alpha_vantage_data(self) -> Optional[Dict[str, Any]]:
        """Hole Gold-Preis von Alpha Vantage"""
        if not self.source_status['alpha_vantage']['available'] or self.alpha_vantage_key == "demo":
            return None
            
        try:
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'CURRENCY_EXCHANGE_RATE',
                'from_currency': 'XAU',
                'to_currency': 'USD',
                'apikey': self.alpha_vantage_key
            }
            
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            
            if 'Realtime Currency Exchange Rate' in data:
                exchange_rate = data['Realtime Currency Exchange Rate']
                current_price = float(exchange_rate.get('5. Exchange Rate', self.last_known_price))
                
                price_data = {
                    'price': current_price,
                    'previous_close': self.last_known_price,
                    'change': current_price - self.last_known_price,
                    'change_percent': (current_price - self.last_known_price) / self.last_known_price * 100,
                    'timestamp': datetime.utcnow().isoformat(),
                    'source': 'alpha_vantage',
                    'currency': 'USD'
                }
                
                self.last_known_price = current_price
                self._update_source_status('alpha_vantage', True)
                return price_data
                
        except Exception as e:
            error_msg = f"Alpha Vantage API error: {str(e)}"
            logger.error(error_msg)
            self._update_source_status('alpha_vantage', False, error_msg)
            
        return None
    
    def get_realistic_simulation(self) -> Dict[str, Any]:
        """Generiere realistische simulierte Daten basierend auf letztem bekannten Preis"""
        # Simuliere realistische Preisbewegungen
        volatility = 0.001  # 0.1% Volatilität
        trend_strength = 0.0001  # Leichter Trend
        
        # Random Walk mit Trend
        price_change = random.gauss(self.price_trend * trend_strength, volatility * self.last_known_price)
        new_price = self.last_known_price + price_change
        
        # Update Trend (Mean Reversion)
        self.price_trend = self.price_trend * 0.95 + random.gauss(0, 0.1)
        
        # Begrenze extreme Bewegungen
        max_change = self.last_known_price * 0.01  # Max 1% Bewegung
        price_change = max(-max_change, min(max_change, price_change))
        new_price = self.last_known_price + price_change
        
        previous_price = self.last_known_price
        self.last_known_price = new_price
        
        return {
            'price': round(new_price, 2),
            'previous_close': round(previous_price, 2),
            'change': round(price_change, 2),
            'change_percent': round((price_change / previous_price * 100), 4),
            'timestamp': datetime.utcnow().isoformat(),
            'source': 'simulation',
            'currency': 'USD',
            'market_state': 'SIMULATED'
        }
    
    def get_current_price(self) -> Dict[str, Any]:
        """Hole aktuellen Gold-Preis mit intelligenter Fallback-Logik"""
        cache_key = 'current_price'
        
        # Prüfe Cache
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key][0]
        
        # Versuche verschiedene Datenquellen in Prioritätsreihenfolge
        data_sources = [
            ('yahoo_finance', self.get_yahoo_finance_data),
            ('alpha_vantage', self.get_alpha_vantage_data)
        ]
        
        for source_name, source_func in data_sources:
            if self.source_status[source_name]['available']:
                try:
                    price_data = source_func()
                    if price_data:
                        # Cache erfolgreiche Daten
                        self.cache[cache_key] = (price_data, time.time())
                        logger.info(f"Successfully retrieved price from {source_name}: ${price_data['price']}")
                        return price_data
                except Exception as e:
                    logger.error(f"Error retrieving data from {source_name}: {e}")
                    continue
        
        # Fallback zu realistischer Simulation
        logger.warning("All data sources failed, using realistic simulation")
        price_data = self.get_realistic_simulation()
        self.cache[cache_key] = (price_data, time.time())
        return price_data

    def get_historical_data(self, days: int = 7, interval: str = '1m') -> List[Dict[str, Any]]:
        """Hole historische Gold-Daten mit verbesserter Logik"""
        cache_key = f'historical_{days}_{interval}'

        # Prüfe Cache
        if self._is_cache_valid(cache_key, self.historical_cache_duration):
            return self.cache[cache_key][0]

        # Versuche Yahoo Finance für historische Daten
        historical_data = self._get_yahoo_historical_data(days, interval)

        if not historical_data:
            # Fallback zu simulierten historischen Daten
            logger.warning("Yahoo Finance historical data failed, generating simulated data")
            historical_data = self._generate_realistic_historical_data(days, interval)

        # Cache das Ergebnis
        if historical_data:
            self.cache[cache_key] = (historical_data, time.time())

        return historical_data

    def _get_yahoo_historical_data(self, days: int, interval: str) -> Optional[List[Dict[str, Any]]]:
        """Hole historische Daten von Yahoo Finance"""
        try:
            end_time = int(time.time())
            start_time = end_time - (days * 24 * 60 * 60)

            url = "https://query1.finance.yahoo.com/v8/finance/chart/XAUUSD=X"
            params = {
                'period1': start_time,
                'period2': end_time,
                'interval': interval,
                'includePrePost': 'false'
            }
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9'
            }

            response = requests.get(url, params=params, headers=headers, timeout=20)
            response.raise_for_status()

            data = response.json()

            if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                result = data['chart']['result'][0]
                timestamps = result.get('timestamp', [])
                indicators = result.get('indicators', {})
                quote = indicators.get('quote', [{}])[0]

                opens = quote.get('open', [])
                highs = quote.get('high', [])
                lows = quote.get('low', [])
                closes = quote.get('close', [])
                volumes = quote.get('volume', [])

                historical_data = []
                for i in range(len(timestamps)):
                    # Skip None values
                    if (i < len(opens) and opens[i] is not None and
                        i < len(highs) and highs[i] is not None and
                        i < len(lows) and lows[i] is not None and
                        i < len(closes) and closes[i] is not None):

                        historical_data.append({
                            'timestamp': datetime.fromtimestamp(timestamps[i]).isoformat(),
                            'open': round(float(opens[i]), 2),
                            'high': round(float(highs[i]), 2),
                            'low': round(float(lows[i]), 2),
                            'close': round(float(closes[i]), 2),
                            'volume': int(volumes[i]) if i < len(volumes) and volumes[i] else 0
                        })

                logger.info(f"Retrieved {len(historical_data)} historical data points from Yahoo Finance")
                return historical_data

        except Exception as e:
            logger.error(f"Error retrieving historical data from Yahoo Finance: {e}")

        return None

    def _generate_realistic_historical_data(self, days: int, interval: str) -> List[Dict[str, Any]]:
        """Generiere realistische historische Daten"""
        # Berechne Anzahl der Datenpunkte basierend auf Intervall
        if interval == '1m':
            points_per_day = 1440  # 24 * 60
        elif interval == '5m':
            points_per_day = 288   # 24 * 12
        elif interval == '1h':
            points_per_day = 24
        else:
            points_per_day = 1440  # Default zu 1m

        total_points = min(days * points_per_day, 1000)  # Begrenze auf 1000 Punkte

        historical_data = []
        current_time = datetime.utcnow() - timedelta(days=days)

        # Starte mit letztem bekannten Preis oder Basis-Preis
        price = self.last_known_price

        for i in range(total_points):
            # Simuliere realistische OHLC-Daten
            volatility = 0.002  # 0.2% Volatilität

            # Open ist der Close des vorherigen Candles
            open_price = price

            # Simuliere High/Low basierend auf Volatilität
            high_low_range = price * volatility * random.uniform(0.5, 2.0)
            high_price = open_price + random.uniform(0, high_low_range)
            low_price = open_price - random.uniform(0, high_low_range)

            # Close basierend auf Random Walk
            price_change = random.gauss(0, price * volatility)
            close_price = open_price + price_change

            # Stelle sicher, dass Close zwischen High und Low liegt
            close_price = max(low_price, min(high_price, close_price))

            # Update price für nächsten Candle
            price = close_price

            # Simuliere Volume
            base_volume = 1000
            volume = int(base_volume * random.uniform(0.5, 2.0))

            historical_data.append({
                'timestamp': current_time.isoformat(),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume
            })

            # Increment time based on interval
            if interval == '1m':
                current_time += timedelta(minutes=1)
            elif interval == '5m':
                current_time += timedelta(minutes=5)
            elif interval == '1h':
                current_time += timedelta(hours=1)
            else:
                current_time += timedelta(minutes=1)

        logger.info(f"Generated {len(historical_data)} simulated historical data points")
        return historical_data

    def get_market_status(self) -> Dict[str, Any]:
        """Hole aktuellen Marktstatus"""
        current_time = datetime.utcnow()
        hour = current_time.hour

        # Bestimme Handelssession basierend auf UTC-Zeit
        if 13 <= hour < 22:  # London/NY Session (13:00-22:00 UTC)
            session = "London/NY"
            activity = "Hoch"
            is_open = True
        elif 0 <= hour < 9:   # Asien Session (00:00-09:00 UTC)
            session = "Asien"
            activity = "Mittel"
            is_open = True
        else:
            session = "Geschlossen"
            activity = "Niedrig"
            is_open = False

        return {
            'session': session,
            'activity_level': activity,
            'market_open': is_open,
            'server_time': current_time.isoformat(),
            'next_session': self._get_next_session(hour),
            'source_status': self.source_status
        }

    def _get_next_session(self, current_hour: int) -> str:
        """Bestimme nächste Handelssession"""
        if current_hour < 13:
            return "London/NY (13:00 UTC)"
        elif current_hour < 22:
            return "Asien (00:00 UTC)"
        else:
            return "London/NY (13:00 UTC)"
