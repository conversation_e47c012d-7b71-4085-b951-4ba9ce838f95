                    <div class="loading">Warte auf Signal...</div>
                </div>
            </div>
        </div>

        <div class="card analysis-panel">
            <h3>🧠 ICT-Analyse</h3>
            <div id="analysisContainer">
                <div class="loading">Keine Analyse verfügbar. Klicken Sie auf "ICT-Analyse starten".</div>
            </div>
        </div>

        <div class="card">
            <h3>📊 Trading-Historie</h3>
            <div id="tradesContainer">
                <div class="loading">Lade Trades...</div>
            </div>
        </div>
    </div>

    <script>
        // Globale Variablen
        let priceChart = null;
        let currentAnalysis = null;
        let autoAnalysisInterval = null;

        // Initialisierung
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            loadConfig();
            refreshData();
            loadTrades();
            
            // Auto-Update alle 30 Sekunden
            setInterval(refreshData, 30000);
        });

        // Chart initialisieren
        function initializeChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Gold Preis (USD)',
                        data: [],
                        borderColor: '#ffd700',
                        backgroundColor: 'rgba(255, 215, 0, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#fff'
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#fff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // API-Aufrufe
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(`/api/trading${endpoint}`, options);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || 'API-Fehler');
                }

                return result;
            } catch (error) {
                console.error('API-Fehler:', error);
                showMessage(error.message, 'error');
                throw error;
            }
        }

        // Marktdaten aktualisieren
        async function refreshData() {
            try {
                const marketData = await apiCall('/market-data?limit=50');
                
                if (marketData.data && marketData.data.length > 0) {
                    updateChart(marketData.data);
                    updateCurrentPrice(marketData.data[marketData.data.length - 1].close);
                }
            } catch (error) {
                console.error('Fehler beim Laden der Marktdaten:', error);
            }
        }

        // Chart aktualisieren
        function updateChart(data) {
            const labels = data.map(item => {
                const date = new Date(item.timestamp);
                return date.toLocaleTimeString('de-DE', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
            });
            
            const prices = data.map(item => item.close);

            priceChart.data.labels = labels;
            priceChart.data.datasets[0].data = prices;
            priceChart.update();
        }

        // Aktuellen Preis aktualisieren
        function updateCurrentPrice(price) {
            document.getElementById('currentPrice').textContent = `$${price.toFixed(2)}`;
        }

        // ICT-Analyse durchführen
        async function analyzeMarket() {
            try {
                showMessage('Führe ICT-Analyse durch...', 'info');
                
                const analysis = await apiCall('/analyze', 'POST');
                currentAnalysis = analysis.analysis;
                
                displayAnalysis(analysis.analysis);
                
                if (analysis.analysis.trading_signal) {
                    displaySignal(analysis.analysis.trading_signal);
                    showMessage('ICT-Analyse abgeschlossen. Trading-Signal generiert!', 'success');
                } else {
                    showMessage('ICT-Analyse abgeschlossen. Kein Trading-Signal generiert.', 'info');
                }
                
            } catch (error) {
                showMessage('Fehler bei der ICT-Analyse: ' + error.message, 'error');
            }
        }

        // Auto-Analyse
        async function autoAnalyze() {
            try {
                showMessage('Führe automatische Analyse durch...', 'info');
                
                const result = await apiCall('/auto-analyze', 'POST');
                currentAnalysis = result.analysis;
                
                displayAnalysis(result.analysis);
                
                if (result.trading_signal) {
                    displaySignal(result.trading_signal);
                }
                
                if (result.auto_trade_executed) {
                    showMessage('Automatischer Trade ausgeführt!', 'success');
                    loadTrades(); // Aktualisiere Trade-Liste
                } else if (result.analysis.trading_signal) {
                    showMessage('Trading-Signal generiert (manueller Modus)', 'info');
                } else {
                    showMessage('Keine Trading-Gelegenheit erkannt', 'info');
                }
                
            } catch (error) {
                showMessage('Fehler bei der Auto-Analyse: ' + error.message, 'error');
            }
        }