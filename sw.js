// Gold Trading App Service Worker
// Version 1.0.0

const CACHE_NAME = 'gold-trading-app-v1.0.0';
const OFFLINE_URL = '/offline.html';

// Resources to cache for offline functionality
const CACHE_URLS = [
  '/',
  '/static/enhanced_index.html',
  '/static/manifest.json',
  '/offline.html'
];

// API endpoints that should be cached
const API_CACHE_URLS = [
  '/api/market-data',
  '/api/ict-analysis'
];

// Install event - cache essential resources
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching essential resources');
        return cache.addAll(CACHE_URLS);
      })
      .then(() => {
        // Force activation of new service worker
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Cache installation failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        // Take control of all clients
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', event => {
  const request = event.request;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // API requests - Network First with Cache Fallback
    event.respondWith(handleApiRequest(request));
  } else if (url.pathname === '/' || url.pathname.startsWith('/static/')) {
    // Static resources - Cache First with Network Fallback
    event.respondWith(handleStaticRequest(request));
  } else {
    // Other requests - Network First
    event.respondWith(handleNetworkFirst(request));
  }
});

// Handle API requests with intelligent caching
async function handleApiRequest(request) {
  const url = new URL(request.url);
  const cacheName = `${CACHE_NAME}-api`;
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful API responses
      const cache = await caches.open(cacheName);
      
      // Clone response for caching
      const responseClone = networkResponse.clone();
      
      // Add timestamp to cached data
      const responseData = await responseClone.json();
      const timestampedData = {
        ...responseData,
        cached_at: new Date().toISOString(),
        cache_duration: getCacheDuration(url.pathname)
      };
      
      // Create new response with timestamped data
      const timestampedResponse = new Response(
        JSON.stringify(timestampedData),
        {
          status: networkResponse.status,
          statusText: networkResponse.statusText,
          headers: networkResponse.headers
        }
      );
      
      cache.put(request, timestampedResponse.clone());
      
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
    
  } catch (error) {
    console.log('Network failed, trying cache for:', request.url);
    
    // Try cache fallback
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      const cachedData = await cachedResponse.json();
      
      // Check if cached data is still valid
      if (isCacheValid(cachedData)) {
        console.log('Serving valid cached data for:', request.url);
        return cachedResponse;
      } else {
        console.log('Cached data expired for:', request.url);
        // Return stale data with warning
        const staleData = {
          ...cachedData,
          warning: 'Daten möglicherweise veraltet - Offline-Modus'
        };
        
        return new Response(JSON.stringify(staleData), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
    
    // Return offline fallback data
    return createOfflineFallback(url.pathname);
  }
}

// Handle static resources with cache-first strategy
async function handleStaticRequest(request) {
  try {
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('Serving from cache:', request.url);
      return cachedResponse;
    }
    
    // Not in cache, try network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache the response
      cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    throw new Error('Network response not ok');
    
  } catch (error) {
    console.log('Failed to load resource:', request.url);
    
    // Return offline page for main document
    if (request.destination === 'document') {
      const cache = await caches.open(CACHE_NAME);
      return cache.match(OFFLINE_URL) || createOfflineResponse();
    }
    
    // For other resources, return a generic error response
    return new Response('Resource not available offline', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// Handle other requests with network-first strategy
async function handleNetworkFirst(request) {
  try {
    return await fetch(request);
  } catch (error) {
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for documents
    if (request.destination === 'document') {
      return cache.match(OFFLINE_URL) || createOfflineResponse();
    }
    
    throw error;
  }
}

// Determine cache duration based on endpoint
function getCacheDuration(pathname) {
  switch (pathname) {
    case '/api/market-data':
      return 60; // 1 minute for market data
    case '/api/ict-analysis':
      return 300; // 5 minutes for analysis
    default:
      return 3600; // 1 hour default
  }
}

// Check if cached data is still valid
function isCacheValid(cachedData) {
  if (!cachedData.cached_at || !cachedData.cache_duration) {
    return false;
  }
  
  const cachedTime = new Date(cachedData.cached_at);
  const now = new Date();
  const ageInSeconds = (now - cachedTime) / 1000;
  
  return ageInSeconds < cachedData.cache_duration;
}

// Create offline fallback responses for different API endpoints
function createOfflineFallback(pathname) {
  let fallbackData;
  
  switch (pathname) {
    case '/api/market-data':
      fallbackData = createFallbackMarketData();
      break;
    case '/api/ict-analysis':
      fallbackData = createFallbackAnalysis();
      break;
    default:
      fallbackData = { error: 'Service nicht verfügbar im Offline-Modus' };
  }
  
  return new Response(JSON.stringify(fallbackData), {
    status: 200,
    headers: { 
      'Content-Type': 'application/json',
      'X-Offline-Response': 'true'
    }
  });
}

// Create fallback market data
function createFallbackMarketData() {
  const basePrice = 2000;
  const data = [];
  const now = new Date();
  
  for (let i = 99; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60000); // 1 minute intervals
    const price = basePrice + (Math.random() - 0.5) * 20;
    
    data.push({
      timestamp: timestamp.toISOString(),
      open: price,
      high: price + Math.random() * 5,
      low: price - Math.random() * 5,
      close: price + (Math.random() - 0.5) * 2,
      volume: Math.floor(Math.random() * 1000) + 500
    });
  }
  
  return {
    ...data,
    offline_mode: true,
    warning: 'Offline-Daten - Nicht für Trading verwenden'
  };
}

// Create fallback analysis
function createFallbackAnalysis() {
  return {
    market_structure: {
      trend: 'sideways',
      strength: 0.5,
      confidence: 0.3,
      current_price: 2000.0
    },
    fair_value_gaps: [],
    liquidity_pools: [],
    premium_discount: {
      zone: 'neutral',
      risk_level: 'medium'
    },
    order_blocks: [],
    breaker_blocks: [],
    trading_signal: null,
    analysis_timestamp: new Date().toISOString(),
    confidence_score: 0.3,
    offline_mode: true,
    warning: 'Offline-Analyse - Nicht für Trading verwenden'
  };
}

// Create basic offline response
function createOfflineResponse() {
  const offlineHTML = `
    <!DOCTYPE html>
    <html lang="de">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Offline - Gold Trading App</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #1e3c72, #2a5298);
                color: white;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                margin: 0;
                text-align: center;
            }
            .offline-container {
                max-width: 500px;
                padding: 40px;
                background: rgba(255,255,255,0.1);
                border-radius: 20px;
                backdrop-filter: blur(10px);
            }
            .offline-icon {
                font-size: 4rem;
                margin-bottom: 20px;
            }
            h1 { color: #ffd700; margin-bottom: 20px; }
            .retry-btn {
                background: #ffd700;
                color: #000;
                border: none;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 1.1rem;
                cursor: pointer;
                margin-top: 20px;
            }
            .retry-btn:hover {
                background: #e6c200;
            }
        </style>
    </head>
    <body>
        <div class="offline-container">
            <div class="offline-icon">📡</div>
            <h1>Offline-Modus</h1>
            <p>Die Gold Trading App ist derzeit offline. Einige Funktionen sind möglicherweise eingeschränkt.</p>
            <p><strong>Verfügbare Offline-Features:</strong></p>
            <ul style="text-align: left; margin: 20px 0;">
                <li>Cached Marktdaten anzeigen</li>
                <li>Letzte ICT-Analyse einsehen</li>
                <li>Konfiguration anpassen</li>
                <li>Offline-Demo-Modus</li>
            </ul>
            <button class="retry-btn" onclick="window.location.reload()">
                🔄 Verbindung erneut versuchen
            </button>
        </div>
    </body>
    </html>
  `;
  
  return new Response(offlineHTML, {
    status: 200,
    headers: { 'Content-Type': 'text/html' }
  });
}

// Handle background sync for when connection is restored
self.addEventListener('sync', event => {
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-market-data') {
    event.waitUntil(syncMarketData());
  }
});

// Sync market data when connection is restored
async function syncMarketData() {
  try {
    console.log('Syncing market data...');
    
    // Fetch fresh market data
    const response = await fetch('/api/market-data');
    if (response.ok) {
      const cache = await caches.open(`${CACHE_NAME}-api`);
      cache.put('/api/market-data', response.clone());
      
      // Notify all clients about the update
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'DATA_SYNCED',
          endpoint: '/api/market-data'
        });
      });
    }
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

// Handle push notifications (for future implementation)
self.addEventListener('push', event => {
  console.log('Push notification received:', event);
  
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body || 'Neues Trading-Signal verfügbar',
      icon: '/static/icon-192.png',
      badge: '/static/badge-72.png',
      tag: 'trading-signal',
      requireInteraction: true,
      actions: [
        {
          action: 'view',
          title: 'Signal anzeigen',
          icon: '/static/action-view.png'
        },
        {
          action: 'dismiss',
          title: 'Ignorieren',
          icon: '/static/action-dismiss.png'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title || 'Gold Trading App', options)
    );
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  console.log('Notification clicked:', event);
  
  event.notification.close();
  
  if (event.action === 'view') {
    // Open the app and focus on trading signals
    event.waitUntil(
      clients.openWindow('/?action=view-signal')
    );
  }
});

// Handle messages from the main app
self.addEventListener('message', event => {
  console.log('Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

console.log('Gold Trading App Service Worker loaded successfully');

