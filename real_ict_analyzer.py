import math
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

class RealICTAnalyzer:
    """Echte ICT-Analyse-Engine für Live-Marktdaten"""
    
    def __init__(self):
        self.min_gap_size = 5.0  # Minimum Gap-Größe in USD
        self.liquidity_threshold = 0.7  # Schwellenwert für Liquiditätspools
        self.structure_lookback = 20  # Anzahl Kerzen für Strukturanalyse
        
    def analyze_market_data(self, ohlc_data: List[Dict]) -> Dict[str, Any]:
        """Hauptanalyse-Funktion für echte Marktdaten"""
        if not ohlc_data or len(ohlc_data) < 10:
            return self.get_empty_analysis()
        
        # Konvertiere zu numerischen Werten
        processed_data = self.process_ohlc_data(ohlc_data)
        
        # Führe alle ICT-Analysen durch
        market_structure = self.analyze_market_structure(processed_data)
        fair_value_gaps = self.identify_fair_value_gaps(processed_data)
        liquidity_pools = self.identify_liquidity_pools(processed_data)
        premium_discount = self.analyze_premium_discount(processed_data)
        order_blocks = self.identify_order_blocks(processed_data)
        
        # Generiere Trading-Signal
        trading_signal = self.generate_trading_signal(
            market_structure, fair_value_gaps, liquidity_pools, 
            premium_discount, order_blocks, processed_data
        )
        
        # Berechne Gesamt-Konfidenz
        confidence_score = self.calculate_confidence_score(
            market_structure, fair_value_gaps, liquidity_pools, premium_discount
        )
        
        return {
            'market_structure': market_structure,
            'fair_value_gaps': fair_value_gaps,
            'liquidity_pools': liquidity_pools,
            'premium_discount': premium_discount,
            'order_blocks': order_blocks,
            'trading_signal': trading_signal,
            'confidence_score': confidence_score,
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'data_points_analyzed': len(processed_data)
        }
    
    def process_ohlc_data(self, ohlc_data: List[Dict]) -> List[Dict]:
        """Verarbeite OHLC-Daten zu numerischen Werten"""
        processed = []
        
        for candle in ohlc_data:
            try:
                processed.append({
                    'timestamp': candle.get('timestamp', ''),
                    'open': float(candle.get('open', 0)),
                    'high': float(candle.get('high', 0)),
                    'low': float(candle.get('low', 0)),
                    'close': float(candle.get('close', 0)),
                    'volume': int(candle.get('volume', 0))
                })
            except (ValueError, TypeError):
                continue
                
        return processed
    
    def analyze_market_structure(self, data: List[Dict]) -> Dict[str, Any]:
        """Analysiere Marktstruktur (Higher Highs/Lower Lows)"""
        if len(data) < self.structure_lookback:
            return {'trend': 'sideways', 'strength': 0.5, 'confidence': 0.3}
        
        recent_data = data[-self.structure_lookback:]
        highs = [candle['high'] for candle in recent_data]
        lows = [candle['low'] for candle in recent_data]
        closes = [candle['close'] for candle in recent_data]
        
        # Identifiziere Swing-Points
        swing_highs = self.find_swing_points(highs, 'high')
        swing_lows = self.find_swing_points(lows, 'low')
        
        # Bestimme Trend
        trend = self.determine_trend(swing_highs, swing_lows)
        
        # Berechne Trend-Stärke
        strength = self.calculate_trend_strength(closes)
        
        # Berechne Konfidenz basierend auf Klarheit der Struktur
        confidence = self.calculate_structure_confidence(swing_highs, swing_lows, trend)
        
        current_price = data[-1]['close']
        
        return {
            'trend': trend,
            'strength': round(strength, 3),
            'confidence': round(confidence, 3),
            'current_price': current_price,
            'swing_highs': swing_highs[-3:] if len(swing_highs) >= 3 else swing_highs,
            'swing_lows': swing_lows[-3:] if len(swing_lows) >= 3 else swing_lows,
            'structure_break': self.detect_structure_break(swing_highs, swing_lows, current_price)
        }
    
    def find_swing_points(self, prices: List[float], point_type: str) -> List[Dict]:
        """Finde Swing-Highs oder Swing-Lows"""
        swing_points = []
        lookback = 3  # Mindestens 3 Kerzen für Swing-Point
        
        for i in range(lookback, len(prices) - lookback):
            if point_type == 'high':
                is_swing = all(prices[i] >= prices[j] for j in range(i-lookback, i+lookback+1) if j != i)
            else:  # low
                is_swing = all(prices[i] <= prices[j] for j in range(i-lookback, i+lookback+1) if j != i)
            
            if is_swing:
                swing_points.append({
                    'price': prices[i],
                    'index': i,
                    'type': point_type
                })
        
        return swing_points
    
    def determine_trend(self, swing_highs: List[Dict], swing_lows: List[Dict]) -> str:
        """Bestimme Trend basierend auf Swing-Points"""
        if len(swing_highs) < 2 or len(swing_lows) < 2:
            return 'sideways'
        
        # Prüfe Higher Highs und Higher Lows für Uptrend
        recent_highs = swing_highs[-2:]
        recent_lows = swing_lows[-2:]
        
        higher_highs = recent_highs[1]['price'] > recent_highs[0]['price']
        higher_lows = recent_lows[1]['price'] > recent_lows[0]['price']
        
        # Prüfe Lower Highs und Lower Lows für Downtrend
        lower_highs = recent_highs[1]['price'] < recent_highs[0]['price']
        lower_lows = recent_lows[1]['price'] < recent_lows[0]['price']
        
        if higher_highs and higher_lows:
            return 'bullish'
        elif lower_highs and lower_lows:
            return 'bearish'
        else:
            return 'sideways'
    
    def calculate_trend_strength(self, closes: List[float]) -> float:
        """Berechne Trend-Stärke basierend auf Preisbewegung"""
        if len(closes) < 10:
            return 0.5
        
        # Berechne gleitenden Durchschnitt
        ma_short = sum(closes[-5:]) / 5
        ma_long = sum(closes[-10:]) / 10
        
        # Berechne Abstand zwischen MAs
        ma_distance = abs(ma_short - ma_long)
        price_range = max(closes[-10:]) - min(closes[-10:])
        
        if price_range == 0:
            return 0.5
        
        strength = min(1.0, ma_distance / price_range * 2)
        return strength
    
    def calculate_structure_confidence(self, swing_highs: List[Dict], swing_lows: List[Dict], trend: str) -> float:
        """Berechne Konfidenz der Strukturanalyse"""
        base_confidence = 0.5
        
        # Mehr Swing-Points = höhere Konfidenz
        if len(swing_highs) >= 3 and len(swing_lows) >= 3:
            base_confidence += 0.2
        
        # Klarer Trend = höhere Konfidenz
        if trend != 'sideways':
            base_confidence += 0.2
        
        # Konsistente Swing-Points = höhere Konfidenz
        if len(swing_highs) >= 2:
            high_consistency = abs(swing_highs[-1]['price'] - swing_highs[-2]['price']) / swing_highs[-1]['price']
            if high_consistency < 0.02:  # Weniger als 2% Unterschied
                base_confidence += 0.1
        
        return min(1.0, base_confidence)
    
    def detect_structure_break(self, swing_highs: List[Dict], swing_lows: List[Dict], current_price: float) -> Optional[Dict]:
        """Erkenne Strukturbrüche"""
        if not swing_highs or not swing_lows:
            return None
        
        last_high = swing_highs[-1]['price'] if swing_highs else current_price
        last_low = swing_lows[-1]['price'] if swing_lows else current_price
        
        # Prüfe auf Durchbruch wichtiger Level
        if current_price > last_high * 1.001:  # 0.1% Puffer
            return {
                'type': 'bullish_break',
                'level': last_high,
                'current_price': current_price
            }
        elif current_price < last_low * 0.999:  # 0.1% Puffer
            return {
                'type': 'bearish_break',
                'level': last_low,
                'current_price': current_price
            }
        
        return None
    
    def identify_fair_value_gaps(self, data: List[Dict]) -> List[Dict]:
        """Identifiziere Fair Value Gaps"""
        gaps = []
        
        for i in range(2, len(data)):
            prev_candle = data[i-2]
            current_candle = data[i-1]
            next_candle = data[i]
            
            # Bullish FVG: Gap zwischen prev_candle high und next_candle low
            if (prev_candle['high'] < next_candle['low'] and 
                next_candle['low'] - prev_candle['high'] >= self.min_gap_size):
                
                gaps.append({
                    'type': 'bullish',
                    'start_price': prev_candle['high'],
                    'end_price': next_candle['low'],
                    'gap_size': next_candle['low'] - prev_candle['high'],
                    'timestamp': current_candle['timestamp'],
                    'filled': self.is_gap_filled(data[i:], prev_candle['high'], next_candle['low']),
                    'strength': self.calculate_gap_strength(prev_candle, current_candle, next_candle)
                })
            
            # Bearish FVG: Gap zwischen prev_candle low und next_candle high
            elif (prev_candle['low'] > next_candle['high'] and 
                  prev_candle['low'] - next_candle['high'] >= self.min_gap_size):
                
                gaps.append({
                    'type': 'bearish',
                    'start_price': prev_candle['low'],
                    'end_price': next_candle['high'],
                    'gap_size': prev_candle['low'] - next_candle['high'],
                    'timestamp': current_candle['timestamp'],
                    'filled': self.is_gap_filled(data[i:], next_candle['high'], prev_candle['low']),
                    'strength': self.calculate_gap_strength(prev_candle, current_candle, next_candle)
                })
        
        # Sortiere nach Stärke und behalte nur die besten
        gaps.sort(key=lambda x: x['strength'], reverse=True)
        return gaps[:5]  # Top 5 Gaps
    
    def is_gap_filled(self, future_data: List[Dict], gap_low: float, gap_high: float) -> bool:
        """Prüfe ob Gap gefüllt wurde"""
        for candle in future_data:
            if candle['low'] <= gap_low and candle['high'] >= gap_high:
                return True
        return False
    
    def calculate_gap_strength(self, prev_candle: Dict, gap_candle: Dict, next_candle: Dict) -> float:
        """Berechne Gap-Stärke basierend auf Volumen und Momentum"""
        base_strength = 0.5
        
        # Volumen-Faktor
        avg_volume = (prev_candle['volume'] + gap_candle['volume'] + next_candle['volume']) / 3
        if gap_candle['volume'] > avg_volume * 1.5:
            base_strength += 0.3
        
        # Momentum-Faktor (Kerzen-Größe)
        gap_candle_size = abs(gap_candle['close'] - gap_candle['open'])
        avg_candle_size = (abs(prev_candle['close'] - prev_candle['open']) + 
                          abs(next_candle['close'] - next_candle['open'])) / 2
        
        if gap_candle_size > avg_candle_size * 1.5:
            base_strength += 0.2
        
        return min(1.0, base_strength)
    
    def identify_liquidity_pools(self, data: List[Dict]) -> List[Dict]:
        """Identifiziere Liquiditätspools (Stop-Loss-Cluster)"""
        pools = []
        
        # Suche nach signifikanten Highs und Lows (potenzielle Stop-Loss-Level)
        for i in range(5, len(data) - 5):
            current = data[i]
            
            # Prüfe auf lokale Highs (Buy-Stops darüber)
            is_local_high = all(current['high'] >= data[j]['high'] for j in range(i-5, i+6) if j != i)
            if is_local_high:
                # Berechne potenzielle Liquidität basierend auf Volumen und Touches
                touches = self.count_level_touches(data, current['high'], tolerance=2.0)
                strength = min(1.0, touches / 5.0)  # Normalisiere auf 0-1
                
                if strength >= self.liquidity_threshold:
                    pools.append({
                        'type': 'buy_stops',
                        'price': current['high'],
                        'timestamp': current['timestamp'],
                        'strength': strength,
                        'touches': touches,
                        'swept': self.is_level_swept(data[i:], current['high'], 'above')
                    })
            
            # Prüfe auf lokale Lows (Sell-Stops darunter)
            is_local_low = all(current['low'] <= data[j]['low'] for j in range(i-5, i+6) if j != i)
            if is_local_low:
                touches = self.count_level_touches(data, current['low'], tolerance=2.0)
                strength = min(1.0, touches / 5.0)
                
                if strength >= self.liquidity_threshold:
                    pools.append({
                        'type': 'sell_stops',
                        'price': current['low'],
                        'timestamp': current['timestamp'],
                        'strength': strength,
                        'touches': touches,
                        'swept': self.is_level_swept(data[i:], current['low'], 'below')
                    })
        
        # Sortiere nach Stärke
        pools.sort(key=lambda x: x['strength'], reverse=True)
        return pools[:10]  # Top 10 Pools
    
    def count_level_touches(self, data: List[Dict], level: float, tolerance: float) -> int:
        """Zähle wie oft ein Level berührt wurde"""
        touches = 0
        for candle in data:
            if abs(candle['high'] - level) <= tolerance or abs(candle['low'] - level) <= tolerance:
                touches += 1
        return touches
    
    def is_level_swept(self, future_data: List[Dict], level: float, direction: str) -> bool:
        """Prüfe ob Level durchbrochen wurde (Liquidität abgeholt)"""
        for candle in future_data:
            if direction == 'above' and candle['high'] > level:
                return True
            elif direction == 'below' and candle['low'] < level:
                return True
        return False
    
    def analyze_premium_discount(self, data: List[Dict]) -> Dict[str, Any]:
        """Analysiere Premium/Discount Zonen"""
        if len(data) < 20:
            return {'zone': 'neutral', 'risk_level': 'medium'}
        
        recent_data = data[-20:]
        highs = [candle['high'] for candle in recent_data]
        lows = [candle['low'] for candle in recent_data]
        
        range_high = max(highs)
        range_low = min(lows)
        range_size = range_high - range_low
        
        current_price = data[-1]['close']
        
        # Berechne Position im Range (0 = Low, 1 = High)
        if range_size == 0:
            position = 0.5
        else:
            position = (current_price - range_low) / range_size
        
        # Bestimme Zone
        if position >= 0.7:
            zone = 'premium'
            risk_level = 'high'
        elif position <= 0.3:
            zone = 'discount'
            risk_level = 'low'
        else:
            zone = 'equilibrium'
            risk_level = 'medium'
        
        return {
            'zone': zone,
            'risk_level': risk_level,
            'position_in_range': round(position, 3),
            'range_high': range_high,
            'range_low': range_low,
            'current_price': current_price
        }
    
    def identify_order_blocks(self, data: List[Dict]) -> List[Dict]:
        """Identifiziere Order Blocks (institutionelle Aufträge)"""
        order_blocks = []
        
        for i in range(10, len(data) - 5):
            current = data[i]
            
            # Suche nach starken Bewegungen (potenzielle institutionelle Aktivität)
            candle_size = abs(current['close'] - current['open'])
            avg_size = sum(abs(data[j]['close'] - data[j]['open']) for j in range(i-10, i)) / 10
            
            # Order Block Kriterien
            if (candle_size > avg_size * 2 and  # Große Kerze
                current['volume'] > sum(data[j]['volume'] for j in range(i-5, i)) / 5 * 1.5):  # Hohes Volumen
                
                # Bullish Order Block
                if current['close'] > current['open']:
                    order_blocks.append({
                        'type': 'bullish',
                        'high': current['high'],
                        'low': current['low'],
                        'timestamp': current['timestamp'],
                        'strength': min(1.0, candle_size / avg_size / 5),
                        'tested': self.is_order_block_tested(data[i:], current['low'], current['high'])
                    })
                
                # Bearish Order Block
                else:
                    order_blocks.append({
                        'type': 'bearish',
                        'high': current['high'],
                        'low': current['low'],
                        'timestamp': current['timestamp'],
                        'strength': min(1.0, candle_size / avg_size / 5),
                        'tested': self.is_order_block_tested(data[i:], current['low'], current['high'])
                    })
        
        return order_blocks[-5:]  # Letzte 5 Order Blocks
    
    def is_order_block_tested(self, future_data: List[Dict], low: float, high: float) -> bool:
        """Prüfe ob Order Block getestet wurde"""
        for candle in future_data:
            if candle['low'] <= high and candle['high'] >= low:
                return True
        return False
    
    def generate_trading_signal(self, market_structure: Dict, fair_value_gaps: List[Dict], 
                              liquidity_pools: List[Dict], premium_discount: Dict, 
                              order_blocks: List[Dict], data: List[Dict]) -> Optional[Dict]:
        """Generiere Trading-Signal basierend auf ICT-Analyse"""
        
        if not data:
            return None
        
        current_price = data[-1]['close']
        signal_strength = 0.0
        signal_type = None
        reasoning_parts = []
        
        # Faktor 1: Marktstruktur
        if market_structure['trend'] == 'bullish' and market_structure['confidence'] > 0.6:
            signal_strength += 0.25
            signal_type = 'BUY'
            reasoning_parts.append(f"Bullische Marktstruktur (Konfidenz: {market_structure['confidence']:.1%})")
        elif market_structure['trend'] == 'bearish' and market_structure['confidence'] > 0.6:
            signal_strength += 0.25
            signal_type = 'SELL'
            reasoning_parts.append(f"Bärische Marktstruktur (Konfidenz: {market_structure['confidence']:.1%})")
        
        # Faktor 2: Fair Value Gaps
        unfilled_gaps = [gap for gap in fair_value_gaps if not gap['filled']]
        if unfilled_gaps:
            strongest_gap = max(unfilled_gaps, key=lambda x: x['strength'])
            if strongest_gap['strength'] > 0.7:
                signal_strength += 0.2
                gap_type = strongest_gap['type'].title()
                reasoning_parts.append(f"{gap_type} FVG bei {strongest_gap['start_price']:.2f}")
        
        # Faktor 3: Premium/Discount
        if premium_discount['zone'] == 'discount' and signal_type == 'BUY':
            signal_strength += 0.15
            reasoning_parts.append("Preis in Discount-Zone")
        elif premium_discount['zone'] == 'premium' and signal_type == 'SELL':
            signal_strength += 0.15
            reasoning_parts.append("Preis in Premium-Zone")
        
        # Faktor 4: Liquiditätspools
        recent_sweeps = [pool for pool in liquidity_pools if pool['swept']]
        if recent_sweeps:
            signal_strength += 0.15
            sweep_type = "Buy-Stops" if recent_sweeps[0]['type'] == 'buy_stops' else "Sell-Stops"
            reasoning_parts.append(f"{sweep_type} wurden kürzlich getroffen")
        
        # Faktor 5: Order Blocks
        relevant_blocks = [block for block in order_blocks if not block['tested']]
        if relevant_blocks and signal_type:
            matching_blocks = [block for block in relevant_blocks 
                             if (block['type'] == 'bullish' and signal_type == 'BUY') or
                                (block['type'] == 'bearish' and signal_type == 'SELL')]
            if matching_blocks:
                signal_strength += 0.1
                reasoning_parts.append("Ungetesteter Order Block vorhanden")
        
        # Generiere Signal nur bei ausreichender Stärke
        if signal_strength >= 0.5 and signal_type:
            # Berechne Entry, Stop Loss und Take Profit
            entry_price = current_price
            
            if signal_type == 'BUY':
                stop_loss = current_price - (current_price * 0.005)  # 0.5% Stop
                take_profit = current_price + (current_price * 0.0075)  # 0.75% Target (1:1.5 RR)
            else:  # SELL
                stop_loss = current_price + (current_price * 0.005)
                take_profit = current_price - (current_price * 0.0075)
            
            return {
                'signal_type': signal_type,
                'confidence': round(signal_strength, 3),
                'entry_price': round(entry_price, 2),
                'stop_loss': round(stop_loss, 2),
                'take_profit': round(take_profit, 2),
                'risk_reward_ratio': 1.5,
                'reasoning': '; '.join(reasoning_parts),
                'timestamp': datetime.utcnow().isoformat(),
                'expected_duration': '1-4 Stunden'
            }
        
        return None
    
    def calculate_confidence_score(self, market_structure: Dict, fair_value_gaps: List[Dict], 
                                 liquidity_pools: List[Dict], premium_discount: Dict) -> float:
        """Berechne Gesamt-Konfidenz der Analyse"""
        confidence_factors = []
        
        # Marktstruktur-Konfidenz
        confidence_factors.append(market_structure.get('confidence', 0.5))
        
        # FVG-Konfidenz (basierend auf Anzahl und Stärke)
        if fair_value_gaps:
            avg_gap_strength = sum(gap['strength'] for gap in fair_value_gaps) / len(fair_value_gaps)
            confidence_factors.append(avg_gap_strength)
        else:
            confidence_factors.append(0.3)
        
        # Liquiditätspool-Konfidenz
        if liquidity_pools:
            avg_pool_strength = sum(pool['strength'] for pool in liquidity_pools) / len(liquidity_pools)
            confidence_factors.append(avg_pool_strength)
        else:
            confidence_factors.append(0.3)
        
        # Premium/Discount-Konfidenz
        pd_confidence = 0.8 if premium_discount['zone'] != 'equilibrium' else 0.5
        confidence_factors.append(pd_confidence)
        
        # Gewichteter Durchschnitt
        return round(sum(confidence_factors) / len(confidence_factors), 3)
    
    def calculate_lot_size(self, account_balance: float, risk_percentage: float, 
                          entry_price: float, stop_loss: float) -> float:
        """Berechne optimale Lotgröße basierend auf Risikomanagement"""
        risk_amount = account_balance * (risk_percentage / 100)
        risk_in_price = abs(entry_price - stop_loss)
        
        if risk_in_price == 0:
            return 0.01
        
        # Gold: 1 Lot = 100 Unzen, 1 Pip = $0.01
        pip_value = 0.01
        pip_size = 0.01
        risk_in_pips = risk_in_price / pip_size
        
        if risk_in_pips > 0:
            lot_size = risk_amount / (risk_in_pips * pip_value * 100)
            # Begrenze Lotgröße auf vernünftige Werte
            max_lot_size = account_balance / 1000  # Max 1 Lot pro $1000
            lot_size = min(lot_size, max_lot_size)
            return max(round(lot_size, 2), 0.01)
        
        return 0.01
    
    def get_empty_analysis(self) -> Dict[str, Any]:
        """Leere Analyse für unzureichende Daten"""
        return {
            'market_structure': {'trend': 'sideways', 'strength': 0.5, 'confidence': 0.3},
            'fair_value_gaps': [],
            'liquidity_pools': [],
            'premium_discount': {'zone': 'neutral', 'risk_level': 'medium'},
            'order_blocks': [],
            'trading_signal': None,
            'confidence_score': 0.3,
            'analysis_timestamp': datetime.utcnow().isoformat(),
            'data_points_analyzed': 0
        }

# Globale Instanz
real_ict_analyzer = RealICTAnalyzer()

