import os
import time
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS
import json
from datetime import datetime, timedelta

# Import der echten Daten-Module
from real_market_data import real_market_data
from real_ict_analyzer import real_ict_analyzer

app = Flask(__name__)
CORS(app)

# Globale Konfiguration (wie im Original)
app_config = {
    'account_balance': 100.0,
    'risk_percentage': 10.0,
    'auto_trading_enabled': True,
    'risk_reward_ratio': 1.0,
    'max_concurrent_trades': 3,
    'trading_session_active': True
}

# Trading-Status (wie im Original)
trading_status = {
    'active_trades': 0,
    'total_pnl': 0.0,
    'last_signal': None,
    'last_analysis': None,
    'session_start': datetime.utcnow()
}

@app.route('/')
def index():
    """Hauptseite - Original HTML beibehalten"""
    return send_from_directory('static', 'index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """Statische Dateien servieren"""
    return send_from_directory('static', filename)

@app.route('/api/market-data')
def api_market_data():
    """Echte Marktdaten-API"""
    try:
        # Hole echte historische Daten für Chart
        historical_data = real_market_data.get_historical_data(days=7)
        
        # Hole aktuellen Preis
        current_price_data = real_market_data.get_current_price()
        
        # Hole Marktstatus
        market_status = real_market_data.get_market_status()
        
        # Formatiere für Chart.js (wie im Original)
        chart_data = []
        if historical_data:
            # Nehme nur die letzten 100 Datenpunkte für bessere Performance
            recent_data = historical_data[-100:]
            
            for item in recent_data:
                chart_data.append({
                    'x': item['timestamp'],
                    'y': item['close']
                })
        
        response_data = {
            'chart_data': chart_data,
            'current_price': current_price_data['price'],
            'price_change': current_price_data['change'],
            'price_change_percent': current_price_data['change_percent'],
            'market_status': market_status,
            'timestamp': datetime.utcnow().isoformat(),
            'source': current_price_data['source']
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Market data error: {e}")
        return jsonify({'error': 'Market data unavailable', 'message': str(e)}), 500

@app.route('/api/ict-analysis')
def api_ict_analysis():
    """Echte ICT-Analyse-API"""
    try:
        # Hole historische Daten für Analyse
        historical_data = real_market_data.get_historical_data(days=5)
        
        if not historical_data:
            return jsonify({'error': 'Insufficient data for analysis'}), 500
        
        # Führe echte ICT-Analyse durch
        analysis = real_ict_analyzer.analyze_market_data(historical_data)
        
        # Speichere letzte Analyse
        trading_status['last_analysis'] = analysis
        
        # Berechne Lotgröße für potenzielle Signale
        if analysis.get('trading_signal'):
            signal = analysis['trading_signal']
            lot_size = real_ict_analyzer.calculate_lot_size(
                app_config['account_balance'],
                app_config['risk_percentage'],
                signal['entry_price'],
                signal['stop_loss']
            )
            signal['lot_size'] = lot_size
            trading_status['last_signal'] = signal
        
        # Formatiere Response wie im Original
        response = {
            'market_structure': {
                'trend': analysis['market_structure']['trend'],
                'strength': analysis['market_structure']['strength'],
                'confidence': analysis['market_structure']['confidence'],
                'current_price': analysis['market_structure']['current_price']
            },
            'fair_value_gaps': [
                {
                    'type': gap['type'],
                    'start_price': gap['start_price'],
                    'end_price': gap['end_price'],
                    'gap_size': gap['gap_size'],
                    'filled': gap['filled'],
                    'strength': gap['strength']
                }
                for gap in analysis['fair_value_gaps'][:5]  # Top 5
            ],
            'liquidity_pools': [
                {
                    'type': pool['type'],
                    'price': pool['price'],
                    'strength': pool['strength'],
                    'swept': pool['swept']
                }
                for pool in analysis['liquidity_pools'][:5]  # Top 5
            ],
            'premium_discount': analysis['premium_discount'],
            'order_blocks': [
                {
                    'type': block['type'],
                    'high': block['high'],
                    'low': block['low'],
                    'strength': block['strength'],
                    'tested': block['tested']
                }
                for block in analysis['order_blocks']
            ],
            'trading_signal': analysis['trading_signal'],
            'confidence_score': analysis['confidence_score'],
            'analysis_timestamp': analysis['analysis_timestamp']
        }
        
        return jsonify(response)
        
    except Exception as e:
        print(f"ICT analysis error: {e}")
        return jsonify({'error': 'Analysis failed', 'message': str(e)}), 500

@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """Konfiguration verwalten (wie im Original)"""
    try:
        if request.method == 'GET':
            return jsonify(app_config)
        
        elif request.method == 'POST':
            data = request.get_json()
            
            # Aktualisiere Konfiguration
            for key, value in data.items():
                if key in app_config:
                    app_config[key] = value
            
            return jsonify({'status': 'success', 'config': app_config})
            
    except Exception as e:
        return jsonify({'error': 'Configuration error', 'message': str(e)}), 500

@app.route('/api/trading-status')
def api_trading_status():
    """Trading-Status abrufen (wie im Original)"""
    try:
        # Aktualisiere Kontostand basierend auf aktueller Konfiguration
        current_price_data = real_market_data.get_current_price()
        
        status = {
            'account_balance': app_config['account_balance'],
            'active_trades': trading_status['active_trades'],
            'total_pnl': trading_status['total_pnl'],
            'current_price': current_price_data['price'],
            'price_change': current_price_data['change'],
            'price_change_percent': current_price_data['change_percent'],
            'last_signal': trading_status['last_signal'],
            'session_uptime': str(datetime.utcnow() - trading_status['session_start']),
            'auto_trading': app_config['auto_trading_enabled'],
            'risk_percentage': app_config['risk_percentage'],
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({'error': 'Status unavailable', 'message': str(e)}), 500

@app.route('/api/execute-trade', methods=['POST'])
def api_execute_trade():
    """Trade ausführen (Demo-Modus wie im Original)"""
    try:
        data = request.get_json()
        
        # Hole aktuellen Preis für realistische Simulation
        current_price_data = real_market_data.get_current_price()
        
        # Simuliere Trade-Ausführung mit echten Preisen
        trade_result = {
            'trade_id': f"TRADE_{int(time.time())}",
            'status': 'executed',
            'signal_type': data.get('signal_type', 'BUY'),
            'entry_price': current_price_data['price'],
            'lot_size': data.get('lot_size', 0.01),
            'timestamp': datetime.utcnow().isoformat(),
            'market_price': current_price_data['price'],
            'spread': 0.5,  # Typischer Gold-Spread
            'estimated_profit': 0.0  # Wird später basierend auf Marktbewegung berechnet
        }
        
        # Aktualisiere Trading-Status
        trading_status['active_trades'] += 1
        
        return jsonify(trade_result)
        
    except Exception as e:
        return jsonify({'error': 'Trade execution failed', 'message': str(e)}), 500

@app.route('/api/auto-analysis')
def api_auto_analysis():
    """Automatische Analyse (wie im Original, aber mit echten Daten)"""
    try:
        # Hole echte Daten
        historical_data = real_market_data.get_historical_data(days=3)
        current_price_data = real_market_data.get_current_price()
        
        if not historical_data:
            return jsonify({'error': 'Insufficient data'}), 500
        
        # Führe Analyse durch
        analysis = real_ict_analyzer.analyze_market_data(historical_data)
        
        # Erstelle automatische Empfehlung
        auto_result = {
            'analysis_completed': True,
            'current_price': current_price_data['price'],
            'market_trend': analysis['market_structure']['trend'],
            'confidence': analysis['confidence_score'],
            'recommendation': 'HOLD',  # Standard
            'reasoning': 'Automatische Analyse basierend auf echten Marktdaten',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Generiere Empfehlung basierend auf Signal
        if analysis.get('trading_signal'):
            signal = analysis['trading_signal']
            auto_result['recommendation'] = signal['signal_type']
            auto_result['reasoning'] = signal['reasoning']
            auto_result['entry_price'] = signal['entry_price']
            auto_result['stop_loss'] = signal['stop_loss']
            auto_result['take_profit'] = signal['take_profit']
            
            # Berechne Lotgröße
            lot_size = real_ict_analyzer.calculate_lot_size(
                app_config['account_balance'],
                app_config['risk_percentage'],
                signal['entry_price'],
                signal['stop_loss']
            )
            auto_result['lot_size'] = lot_size
        
        return jsonify(auto_result)
        
    except Exception as e:
        return jsonify({'error': 'Auto-analysis failed', 'message': str(e)}), 500

@app.route('/api/trading-history')
def api_trading_history():
    """Trading-Historie (erweitert mit echten Daten)"""
    try:
        # Hole historische Preisdaten für Kontext
        historical_data = real_market_data.get_historical_data(days=30)
        
        # Simuliere Trading-Historie basierend auf echten Preisbewegungen
        history = []
        
        if historical_data and len(historical_data) > 10:
            # Erstelle realistische Trading-Historie
            for i in range(min(10, len(historical_data) - 5)):
                data_point = historical_data[-(i+5)]
                
                # Simuliere Trade basierend auf echten Preisbewegungen
                entry_price = data_point['close']
                exit_price = historical_data[-(i+1)]['close']
                
                trade_type = 'BUY' if exit_price > entry_price else 'SELL'
                pnl = (exit_price - entry_price) * 0.01 if trade_type == 'BUY' else (entry_price - exit_price) * 0.01
                
                history.append({
                    'trade_id': f"HIST_{i+1}",
                    'timestamp': data_point['timestamp'],
                    'type': trade_type,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'lot_size': 0.01,
                    'pnl': round(pnl, 2),
                    'status': 'closed'
                })
        
        return jsonify({
            'trades': history,
            'total_trades': len(history),
            'profitable_trades': len([t for t in history if t['pnl'] > 0]),
            'total_pnl': sum(t['pnl'] for t in history),
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': 'History unavailable', 'message': str(e)}), 500

# Error Handler (wie im Original)
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🚀 Starte Gold Trading App mit echten Marktdaten...")
    print("📡 Marktdaten-Quelle: Yahoo Finance + Fallbacks")
    print("🧠 ICT-Analyse: Erweiterte Engine mit echten Daten")
    print("💰 Aktueller Gold-Preis wird geladen...")
    
    # Teste Verbindung zu Marktdaten
    try:
        current_price = real_market_data.get_current_price()
        print(f"✅ Verbindung erfolgreich - Gold: ${current_price['price']:.2f} ({current_price['source']})")
    except Exception as e:
        print(f"⚠️ Marktdaten-Warnung: {e}")
    
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)

