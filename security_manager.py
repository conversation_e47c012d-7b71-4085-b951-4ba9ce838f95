import hashlib
import hmac
import time
import json
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from functools import wraps
import logging

class SecurityManager:
    """
    Sicherheits-Manager für die Trading-App
    Implementiert GDPR-konforme Sicherheitsmaßnahmen
    """
    
    def __init__(self):
        self.secret_key = secrets.token_hex(32)
        self.rate_limits = {}
        self.blocked_ips = set()
        self.session_tokens = {}
        
        # GDPR-konforme Logging-Konfiguration
        self.setup_logging()
        
        # Sicherheits-Konfiguration
        self.config = {
            'max_requests_per_minute': 60,
            'max_requests_per_hour': 1000,
            'session_timeout_minutes': 30,
            'max_failed_attempts': 5,
            'block_duration_minutes': 15,
            'require_https': True,
            'data_retention_days': 30
        }
    
    def setup_logging(self):
        """
        Konfiguriert GDPR-konformes Logging
        """
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/tmp/trading_app_security.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def rate_limit_decorator(self, max_per_minute: int = None):
        """
        Decorator für Rate Limiting
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Extrahiere IP aus Request (vereinfacht)
                client_ip = self._get_client_ip()
                
                if not self.check_rate_limit(client_ip, max_per_minute):
                    self.logger.warning(f"Rate limit exceeded for IP: {self._hash_ip(client_ip)}")
                    raise SecurityException("Rate limit exceeded")
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def check_rate_limit(self, client_ip: str, max_per_minute: int = None) -> bool:
        """
        Prüft Rate Limits für Client IP
        """
        if client_ip in self.blocked_ips:
            return False
        
        max_requests = max_per_minute or self.config['max_requests_per_minute']
        current_time = time.time()
        
        # Initialisiere IP-Tracking
        if client_ip not in self.rate_limits:
            self.rate_limits[client_ip] = {
                'requests': [],
                'failed_attempts': 0,
                'last_attempt': current_time
            }
        
        ip_data = self.rate_limits[client_ip]
        
        # Entferne alte Requests (älter als 1 Minute)
        ip_data['requests'] = [req_time for req_time in ip_data['requests'] 
                              if current_time - req_time < 60]
        
        # Prüfe Rate Limit
        if len(ip_data['requests']) >= max_requests:
            self._handle_rate_limit_violation(client_ip)
            return False
        
        # Füge aktuellen Request hinzu
        ip_data['requests'].append(current_time)
        ip_data['last_attempt'] = current_time
        
        return True
    
    def _handle_rate_limit_violation(self, client_ip: str):
        """
        Behandelt Rate Limit Verletzungen
        """
        ip_data = self.rate_limits[client_ip]
        ip_data['failed_attempts'] += 1
        
        if ip_data['failed_attempts'] >= self.config['max_failed_attempts']:
            self.blocked_ips.add(client_ip)
            self.logger.warning(f"IP blocked due to repeated violations: {self._hash_ip(client_ip)}")
            
            # Automatisches Unblocking nach konfigurierter Zeit
            self._schedule_unblock(client_ip)
    
    def _schedule_unblock(self, client_ip: str):
        """
        Plant automatisches Unblocking (vereinfacht)
        """
        # In einer echten Implementierung würde hier ein Timer/Scheduler verwendet
        pass
    
    def validate_trading_request(self, request_data: Dict) -> Tuple[bool, str]:
        """
        Validiert Trading-Requests auf Sicherheit
        """
        try:
            # 1. Prüfe erforderliche Felder
            required_fields = ['signal_type', 'entry_price', 'stop_loss', 'take_profit']
            for field in required_fields:
                if field not in request_data:
                    return False, f"Fehlendes Feld: {field}"
            
            # 2. Validiere Werte
            signal_type = request_data['signal_type']
            if signal_type not in ['BUY', 'SELL']:
                return False, "Ungültiger Signal-Typ"
            
            # 3. Prüfe Preis-Plausibilität
            entry_price = float(request_data['entry_price'])
            stop_loss = float(request_data['stop_loss'])
            take_profit = float(request_data['take_profit'])
            
            # Gold-Preis sollte in realistischem Bereich sein
            if not (1500 <= entry_price <= 3000):
                return False, "Unrealistischer Entry-Preis"
            
            # Risk-Reward Verhältnis prüfen
            if signal_type == 'BUY':
                risk = entry_price - stop_loss
                reward = take_profit - entry_price
            else:
                risk = stop_loss - entry_price
                reward = entry_price - take_profit
            
            if risk <= 0 or reward <= 0:
                return False, "Ungültiges Risk-Reward Verhältnis"
            
            rr_ratio = reward / risk
            if rr_ratio < 0.5 or rr_ratio > 5.0:
                return False, "Unrealistisches Risk-Reward Verhältnis"
            
            # 4. Prüfe Lotgröße
            lot_size = request_data.get('lot_size', 0)
            if lot_size <= 0 or lot_size > 10:  # Max 10 Lots
                return False, "Ungültige Lotgröße"
            
            return True, "Validierung erfolgreich"
            
        except (ValueError, TypeError) as e:
            return False, f"Validierungsfehler: {str(e)}"
    
    def sanitize_input(self, data: any) -> any:
        """
        Bereinigt Eingabedaten
        """
        if isinstance(data, str):
            # Entferne potentiell gefährliche Zeichen
            dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '{', '}']
            for char in dangerous_chars:
                data = data.replace(char, '')
            return data.strip()
        
        elif isinstance(data, dict):
            return {key: self.sanitize_input(value) for key, value in data.items()}
        
        elif isinstance(data, list):
            return [self.sanitize_input(item) for item in data]
        
        return data
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """
        Verschlüsselt sensible Daten (vereinfacht)
        """
        # In einer echten Implementierung würde hier AES oder ähnliches verwendet
        return hashlib.sha256((data + self.secret_key).encode()).hexdigest()
    
    def create_session_token(self, user_id: str = "anonymous") -> str:
        """
        Erstellt sicheren Session-Token
        """
        token = secrets.token_urlsafe(32)
        expiry = datetime.utcnow() + timedelta(minutes=self.config['session_timeout_minutes'])
        
        self.session_tokens[token] = {
            'user_id': user_id,
            'created': datetime.utcnow(),
            'expires': expiry,
            'last_activity': datetime.utcnow()
        }
        
        return token
    
    def validate_session_token(self, token: str) -> bool:
        """
        Validiert Session-Token
        """
        if token not in self.session_tokens:
            return False
        
        session = self.session_tokens[token]
        
        # Prüfe Ablaufzeit
        if datetime.utcnow() > session['expires']:
            del self.session_tokens[token]
            return False
        
        # Aktualisiere letzte Aktivität
        session['last_activity'] = datetime.utcnow()
        return True
    
    def get_security_headers(self) -> Dict[str, str]:
        """
        Gibt Sicherheits-Header für HTTP-Responses zurück
        """
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
    
    def log_security_event(self, event_type: str, details: Dict):
        """
        Loggt Sicherheitsereignisse GDPR-konform
        """
        # Anonymisiere IP-Adressen
        if 'client_ip' in details:
            details['client_ip_hash'] = self._hash_ip(details['client_ip'])
            del details['client_ip']
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'details': details
        }
        
        self.logger.info(f"Security Event: {json.dumps(log_entry)}")
    
    def _hash_ip(self, ip: str) -> str:
        """
        Hasht IP-Adresse für GDPR-Konformität
        """
        return hashlib.sha256((ip + self.secret_key).encode()).hexdigest()[:16]
    
    def _get_client_ip(self) -> str:
        """
        Extrahiert Client-IP (vereinfacht)
        """
        # In einer echten Flask-App würde hier request.remote_addr verwendet
        return "127.0.0.1"
    
    def cleanup_old_data(self):
        """
        Bereinigt alte Daten gemäß GDPR-Anforderungen
        """
        current_time = datetime.utcnow()
        retention_period = timedelta(days=self.config['data_retention_days'])
        
        # Bereinige alte Session-Tokens
        expired_tokens = [
            token for token, session in self.session_tokens.items()
            if current_time - session['created'] > retention_period
        ]
        
        for token in expired_tokens:
            del self.session_tokens[token]
        
        # Bereinige alte Rate-Limit-Daten
        cutoff_time = time.time() - (retention_period.total_seconds())
        
        for ip in list(self.rate_limits.keys()):
            ip_data = self.rate_limits[ip]
            if ip_data['last_attempt'] < cutoff_time:
                del self.rate_limits[ip]
        
        self.logger.info(f"Cleaned up old data: {len(expired_tokens)} tokens, rate limit data")
    
    def get_privacy_policy_text(self) -> str:
        """
        Gibt GDPR-konforme Datenschutzerklärung zurück
        """
        return """
        DATENSCHUTZERKLÄRUNG - Gold Trading App
        
        1. DATENERHEBUNG
        Wir erheben nur die für den Betrieb der Trading-App notwendigen Daten:
        - Anonymisierte IP-Adressen für Sicherheitszwecke
        - Trading-Parameter und -Signale
        - Technische Logs für Systemstabilität
        
        2. DATENVERWENDUNG
        Ihre Daten werden ausschließlich verwendet für:
        - Bereitstellung der Trading-Funktionalität
        - Sicherheit und Betrugsschutz
        - Systemoptimierung
        
        3. DATENSPEICHERUNG
        - Daten werden maximal 30 Tage gespeichert
        - Automatische Löschung nach Ablauf
        - Keine Weitergabe an Dritte
        
        4. IHRE RECHTE
        Sie haben das Recht auf:
        - Auskunft über gespeicherte Daten
        - Löschung Ihrer Daten
        - Datenportabilität
        
        Kontakt: <EMAIL>
        """

class SecurityException(Exception):
    """Custom Exception für Sicherheitsverletzungen"""
    pass

# Globale Instanz
security_manager = SecurityManager()

